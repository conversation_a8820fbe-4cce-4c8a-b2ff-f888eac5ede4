#!/usr/bin/env python3
"""
混合束搜索：结合简化束搜索的高召回率和高级束搜索的高精确率
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import random
from collections import Counter, defaultdict

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import UnifiedConfig, get_config, set_config
from data import DialogDataset, collate_fn
from models import Graph2SeqECPE, create_ecpe_vocab
from utils import prepare_target_sequences, compute_loss, compute_ecpe_metrics, UnifiedPostProcessor
from utils.advanced_loss import create_advanced_loss_function, AdaptiveLossScheduler
from parser import parse_args_and_create_config, print_args


class SimpleConfidenceEstimator:
    """简单的置信度估计器"""

    def estimate_confidence(self, sequence):
        """估计序列的置信度"""
        # 简单实现：返回固定的置信度值
        if isinstance(sequence, torch.Tensor):
            return torch.ones(sequence.size(0)) * 0.8
        else:
            return torch.tensor([0.8] * len(sequence))


class HybridBeamSearchDecoder:
    """
    混合束搜索解码器：结合简化束搜索的生成策略和高级束搜索的质量控制
    """
    
    def __init__(self, model, token2idx, idx2token, beam_size=5, max_length=20,
                 confidence_threshold=0.2, diversity_weight=0.5, max_pairs_per_sample=8,
                 use_enhanced_post_processing=True):
        self.model = model
        self.token2idx = token2idx
        self.idx2token = idx2token
        self.beam_size = beam_size
        self.max_length = max_length
        self.confidence_threshold = confidence_threshold
        self.diversity_weight = diversity_weight
        self.max_pairs_per_sample = max_pairs_per_sample
        self.use_enhanced_post_processing = use_enhanced_post_processing

        # 特殊token ID
        self.sos_id = token2idx.get('<sos>', 1)
        self.eos_id = token2idx.get('<eos>', 2)
        self.pad_id = token2idx.get('<pad>', 0)
        self.sep_id = token2idx.get('<sep>', 3)

        # 有效的说话者和情感
        self.valid_speakers = {'Chandler', 'Joey', 'Ross', 'Rachel', 'Monica', 'Phoebe', '_NONE'}
        self.valid_emotions = {'surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear'}

        # 统一后处理器
        if self.use_enhanced_post_processing:
            self.post_processor = UnifiedPostProcessor(
                mode='enhanced',
                token2idx=token2idx,
                idx2token=idx2token,
                confidence_threshold=confidence_threshold
            )
        else:
            self.post_processor = UnifiedPostProcessor(mode='optimal')

        # 初始化置信度估计器（简单实现）
        self.confidence_estimator = SimpleConfidenceEstimator()
    
    def hybrid_beam_search(self, batch):
        """
        混合束搜索：高召回率生成 + 高精确率过滤
        """
        self.model.eval()
        device = next(self.model.parameters()).device
        
        with torch.no_grad():
            # 确定批次大小
            if len(batch) >= 10:
                utt_mask = batch[3]
            else:
                utt_mask = batch[3]
            batch_size = utt_mask.size(0)
            
            all_predictions = []
            
            for b in range(batch_size):
                # 1. 计算动态束宽（来自高级束搜索）
                dynamic_beam_size = self._calculate_dynamic_beam_size(batch, b)
                
                # 2. 生成多个高质量候选（简化束搜索的多样性策略）
                candidates = self._generate_diverse_candidates_enhanced(batch, b, dynamic_beam_size)
                
                # 3. 应用多样性约束选择候选（来自高级束搜索）
                selected_candidates = self._select_diverse_candidates(candidates)
                
                # 4. 融合多个候选生成最终序列（新的融合策略）
                final_candidate = self._fuse_candidates(selected_candidates)
                
                # 5. 高级后处理过滤（来自高级束搜索）
                filtered_candidate = self._advanced_post_process_filter(final_candidate)
                
                all_predictions.append(filtered_candidate)

            # 填充序列到相同长度
            if all_predictions:
                max_len = max(len(pred) for pred in all_predictions)
                padded_predictions = []
                for pred in all_predictions:
                    if len(pred) < max_len:
                        # 使用PAD token填充
                        pad_length = max_len - len(pred)
                        padded_pred = torch.cat([pred, torch.full((pad_length,), self.pad_id, device=pred.device)])
                    else:
                        padded_pred = pred
                    padded_predictions.append(padded_pred)
                return torch.stack(padded_predictions)
            else:
                # 如果没有预测结果，返回空张量
                device = next(self.model.parameters()).device
                return torch.empty((batch_size, 0), device=device, dtype=torch.long)
    
    def _calculate_dynamic_beam_size(self, batch, batch_idx):
        """动态束宽计算（来自高级束搜索）"""
        try:
            utt_mask = batch[3][batch_idx]
            valid_utts = utt_mask.sum().item()
            
            utt_speakers = batch[4][batch_idx]
            unique_speakers = len(set(utt_speakers[:valid_utts].tolist()))
            
            utt_emotions = batch[5][batch_idx]
            unique_emotions = len(set(utt_emotions[:valid_utts].tolist()))
            
            # 复杂度评分
            complexity_score = 0
            
            if valid_utts <= 3:
                complexity_score += 1
            elif valid_utts <= 6:
                complexity_score += 2
            else:
                complexity_score += 3
            
            if unique_speakers <= 2:
                complexity_score += 1
            elif unique_speakers <= 4:
                complexity_score += 2
            else:
                complexity_score += 3
            
            if unique_emotions <= 2:
                complexity_score += 1
            elif unique_emotions <= 4:
                complexity_score += 2
            else:
                complexity_score += 3
            
            # 根据复杂度确定束宽（更激进的策略以提高召回率）
            if complexity_score <= 4:
                beam_size = max(3, self.beam_size - 1)  # 简单对话
            elif complexity_score <= 7:
                beam_size = self.beam_size + 1  # 中等复杂度，增加束宽
            else:
                beam_size = min(8, self.beam_size + 3)  # 复杂对话，大幅增加束宽
            
            return beam_size
            
        except Exception as e:
            return self.beam_size + 1  # 默认使用较大的束宽
    
    def _generate_diverse_candidates_enhanced(self, batch, batch_idx, beam_size):
        """
        增强的多样化候选生成（结合简化束搜索的策略）
        """
        candidates = []
        
        # 1. 贪婪解码（高精确率基线）
        greedy_candidate = self._greedy_decode(batch, batch_idx)
        candidates.append(('greedy', greedy_candidate))
        
        # 2. 多种温度采样（简化束搜索的策略，但增加更多温度）
        temperatures = [0.4, 0.6, 0.8, 1.0, 1.2][:beam_size-1]
        
        for i, temp in enumerate(temperatures):
            # 使用不同的随机种子增加多样性
            temp_candidate = self._temperature_sample_with_diversity(batch, batch_idx, temp, candidates, seed=i)
            candidates.append((f'temp_{temp}', temp_candidate))
        
        # 3. 添加高置信度采样（新策略）
        high_conf_candidate = self._high_confidence_sample(batch, batch_idx)
        candidates.append(('high_conf', high_conf_candidate))
        
        return candidates
    
    def _temperature_sample_with_diversity(self, batch, batch_idx, temperature, existing_candidates, seed=0):
        """
        带多样性约束的温度采样（来自高级束搜索，但降低置信度阈值）
        """
        # 设置随机种子增加多样性
        torch.manual_seed(42 + seed)
        
        device = next(self.model.parameters()).device
        single_batch = self._create_single_batch(batch, batch_idx)
        
        sequence = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
        sequence[0] = self.sos_id
        
        for step in range(1, self.max_length):
            try:
                current_seq = sequence[:step+1].unsqueeze(0)
                padded_seq = torch.full((1, self.max_length), self.pad_id, device=device, dtype=torch.long)
                padded_seq[0, :current_seq.size(1)] = current_seq[0]
                
                decoder_outputs, _ = self.model(single_batch, padded_seq, teacher_forcing_ratio=0.0)
                
                if step - 1 < decoder_outputs.size(1):
                    logits = decoder_outputs[0, step - 1, :]
                else:
                    logits = decoder_outputs[0, -1, :]
                
                # 应用温度
                logits = logits / temperature
                probs = torch.softmax(logits, dim=-1)
                
                # 多样性约束（来自高级束搜索）
                if len(existing_candidates) > 0:
                    probs = self._apply_diversity_penalty(probs, sequence[:step], existing_candidates, step)
                
                # 降低置信度阈值以提高召回率
                max_prob = probs.max().item()
                if max_prob < self.confidence_threshold * 0.7:  # 降低阈值
                    sequence[step] = self.eos_id
                    break
                
                # 采样下一个token
                next_token = torch.multinomial(probs, 1).item()
                sequence[step] = next_token
                
                if next_token == self.eos_id:
                    break
                    
            except Exception as e:
                sequence[step] = self.eos_id
                break
        
        return sequence
    
    def _high_confidence_sample(self, batch, batch_idx):
        """
        高置信度采样：专门生成高质量的情感-原因对
        """
        device = next(self.model.parameters()).device
        single_batch = self._create_single_batch(batch, batch_idx)
        
        sequence = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
        sequence[0] = self.sos_id
        
        for step in range(1, self.max_length):
            try:
                current_seq = sequence[:step+1].unsqueeze(0)
                padded_seq = torch.full((1, self.max_length), self.pad_id, device=device, dtype=torch.long)
                padded_seq[0, :current_seq.size(1)] = current_seq[0]
                
                decoder_outputs, _ = self.model(single_batch, padded_seq, teacher_forcing_ratio=0.0)
                
                if step - 1 < decoder_outputs.size(1):
                    logits = decoder_outputs[0, step - 1, :]
                else:
                    logits = decoder_outputs[0, -1, :]
                
                # 使用较低温度确保高置信度
                temperature = 0.5
                logits = logits / temperature
                probs = torch.softmax(logits, dim=-1)
                
                # 只选择高置信度的token
                max_prob = probs.max().item()
                if max_prob < 0.6:  # 高置信度阈值
                    sequence[step] = self.eos_id
                    break
                
                # 选择最高概率的token
                next_token = torch.argmax(probs).item()
                sequence[step] = next_token
                
                if next_token == self.eos_id:
                    break
                    
            except Exception as e:
                sequence[step] = self.eos_id
                break
        
        return sequence
    
    def _apply_diversity_penalty(self, probs, current_sequence, existing_candidates, current_step):
        """应用多样性惩罚（来自高级束搜索）"""
        if len(existing_candidates) == 0:
            return probs
        
        penalty_weights = torch.ones_like(probs)
        
        for _, candidate_seq in existing_candidates:
            if current_step < len(candidate_seq):
                hamming_distance = 0
                min_len = min(len(current_sequence), current_step)
                
                for i in range(min_len):
                    if i < len(candidate_seq) and current_sequence[i] != candidate_seq[i]:
                        hamming_distance += 1
                
                if hamming_distance < min_len * 0.5:
                    if current_step < len(candidate_seq):
                        next_token_id = candidate_seq[current_step].item()
                        if 0 <= next_token_id < len(penalty_weights):
                            penalty_weights[next_token_id] *= (1 - self.diversity_weight)
        
        adjusted_probs = probs * penalty_weights
        adjusted_probs = adjusted_probs / adjusted_probs.sum()
        
        return adjusted_probs
    
    def _select_diverse_candidates(self, candidates):
        """
        选择多样化的候选序列（修改自高级束搜索，保留更多候选）
        """
        if len(candidates) <= 2:
            return candidates
        
        # 计算所有候选的质量分数
        scored_candidates = []
        for method, candidate in candidates:
            quality_score = self._score_candidate_quality(candidate)
            diversity_score = self._calculate_diversity_score(candidate, candidates, method)
            total_score = quality_score + diversity_score * self.diversity_weight
            scored_candidates.append((total_score, method, candidate))
        
        # 按分数排序
        scored_candidates.sort(key=lambda x: x[0], reverse=True)
        
        # 选择前3-4个最佳候选（保留更多以提高召回率）
        num_to_keep = min(4, len(scored_candidates))
        selected = [(method, candidate) for _, method, candidate in scored_candidates[:num_to_keep]]
        
        return selected
    
    def _fuse_candidates(self, candidates):
        """
        融合多个候选序列：新的策略，结合多个候选的优点
        """
        if not candidates:
            device = next(self.model.parameters()).device
            default_seq = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
            default_seq[0] = self.sos_id
            default_seq[1] = self.eos_id
            return default_seq
        
        # 提取所有候选的情感-原因对
        all_pairs = []
        pair_scores = {}
        
        for method, candidate in candidates:
            tokens = [self.idx2token.get(idx.item(), '<unk>') for idx in candidate]
            pairs = self.post_processor.process(tokens)
            
            # 为每个对分配分数
            method_weight = 1.0
            if method == 'greedy':
                method_weight = 1.2  # 贪婪解码权重更高
            elif method == 'high_conf':
                method_weight = 1.1  # 高置信度权重较高
            
            for pair in pairs:
                if pair not in pair_scores:
                    pair_scores[pair] = 0
                pair_scores[pair] += method_weight
        
        # 按分数排序并选择最佳对
        sorted_pairs = sorted(pair_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 限制对的数量但允许更多（提高召回率）
        max_pairs = min(self.max_pairs_per_sample, len(sorted_pairs))
        selected_pairs = [pair for pair, score in sorted_pairs[:max_pairs]]
        
        # 重构序列
        return self._reconstruct_sequence(selected_pairs)
    
    def _reconstruct_sequence(self, pairs):
        """重构token序列"""
        device = next(self.model.parameters()).device
        
        if not pairs:
            default_seq = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
            default_seq[0] = self.eos_id  # 只添加结束标记，不添加<sos>
            return default_seq
        
        # 构建token序列（不添加<sos>标记，因为后处理已经清理过了）
        tokens = []
        for speaker, emotion, cause in pairs:
            tokens.extend([speaker, emotion, cause, '<sep>'])

        # 移除最后一个<sep>并添加<eos>
        if tokens and tokens[-1] == '<sep>':
            tokens[-1] = '<eos>'
        elif tokens:
            tokens.append('<eos>')
        else:
            # 如果没有对，返回空序列
            tokens = ['<eos>']
        
        # 转换为tensor
        sequence = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
        for i, token in enumerate(tokens):
            if i >= self.max_length:
                break
            token_id = self.token2idx.get(token, self.pad_id)
            sequence[i] = token_id
        
        return sequence
    
    def _advanced_post_process_filter(self, sequence):
        """
        高级后处理过滤：使用增强的后处理器或原始逻辑
        """
        if self.use_enhanced_post_processing:
            # 使用增强的后处理器
            try:
                # 调试信息（可选启用）
                if hasattr(self, 'debug_post_processing') and self.debug_post_processing:
                    print(f"[DEBUG] 使用增强后处理，序列长度: {len(sequence)}")

                # 估计置信度
                confidences = self.confidence_estimator.estimate_confidence(sequence)

                # 应用完整的后处理流程
                # 将tensor转换为token列表
                token_sequence = [self.idx2token.get(idx.item(), '<unk>') for idx in sequence]
                processed_results = self.post_processor.process(
                    token_sequence,
                    confidences=confidences
                )

                # 检查处理结果
                if not processed_results or len(processed_results) == 0:
                    if hasattr(self, 'debug_post_processing') and self.debug_post_processing:
                        print(f"[DEBUG] 没有处理结果，返回原序列")
                    return sequence

                # processed_results 格式: [(emo_utt_id, emotion, cause_utt_id), ...]
                # 转换为 _reconstruct_sequence 期望的格式: [(speaker, emotion, cause), ...]
                converted_pairs = []
                for emo_utt_id, emotion, cause_utt_id in processed_results:
                    # 使用话语ID作为说话者（简化处理）
                    converted_pairs.append((emo_utt_id, emotion, cause_utt_id))

                # 调试信息（可选启用）
                if hasattr(self, 'debug_post_processing') and self.debug_post_processing:
                    print(f"[DEBUG] 转换后的对数量: {len(converted_pairs)}")

                # 如果没有有效对，返回原序列
                if len(converted_pairs) == 0:
                    if hasattr(self, 'debug_post_processing') and self.debug_post_processing:
                        print(f"[DEBUG] 没有有效对，返回原序列")
                    return sequence

                # 重构序列
                reconstructed = self._reconstruct_sequence(converted_pairs)
                if hasattr(self, 'debug_post_processing') and self.debug_post_processing:
                    print(f"[DEBUG] 重构序列成功，长度: {len(reconstructed)}")
                return reconstructed

            except Exception as e:
                # 如果增强后处理失败，回退到原始逻辑
                print(f"增强后处理失败，回退到原始逻辑: {e}")
                import traceback
                traceback.print_exc()
                return self._original_post_process_filter(sequence)
        else:
            # 使用原始后处理逻辑
            return self._original_post_process_filter(sequence)

    def _original_post_process_filter(self, sequence):
        """
        原始后处理过滤逻辑（保持向后兼容）
        """
        tokens = [self.idx2token.get(idx.item(), '<unk>') for idx in sequence]
        pairs = self.post_processor.process(tokens)

        # 1. 去重：移除完全相同的情感-原因对
        unique_pairs = []
        seen_pairs = set()
        for pair in pairs:
            if pair not in seen_pairs:
                unique_pairs.append(pair)
                seen_pairs.add(pair)

        # 2. 连续相同情感过滤：放宽到连续4个（而不是3个）
        filtered_pairs = []
        emotion_sequence = [pair[1] for pair in unique_pairs]

        i = 0
        while i < len(unique_pairs):
            # 检查是否有连续4个相同情感
            if i + 3 < len(emotion_sequence):
                if emotion_sequence[i] == emotion_sequence[i+1] == emotion_sequence[i+2] == emotion_sequence[i+3]:
                    # 保留第一个和最后一个，跳过中间的
                    filtered_pairs.append(unique_pairs[i])
                    filtered_pairs.append(unique_pairs[i+3])
                    i += 4
                    continue

            filtered_pairs.append(unique_pairs[i])
            i += 1

        # 3. 说话者一致性检查：保持原有逻辑
        final_pairs = []
        for speaker, emotion, cause in filtered_pairs:
            if speaker in self.valid_speakers and cause in self.valid_speakers and emotion in self.valid_emotions:
                final_pairs.append((speaker, emotion, cause))

        # 4. 重构序列（允许更多对）
        if len(final_pairs) == 0:
            return sequence

        # 限制最大对数（但比高级束搜索更宽松）
        final_pairs = final_pairs[:self.max_pairs_per_sample]

        return self._reconstruct_sequence(final_pairs)
    
    def _greedy_decode(self, batch, batch_idx):
        """贪婪解码"""
        device = next(self.model.parameters()).device
        single_batch = self._create_single_batch(batch, batch_idx)
        
        try:
            prediction = self.model.predict(single_batch, max_length=self.max_length)
            return prediction[0]
        except:
            default_seq = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
            default_seq[0] = self.sos_id
            default_seq[1] = self.eos_id
            return default_seq
    
    def _create_single_batch(self, batch, batch_idx):
        """创建单样本批次"""
        single_batch = []
        for item in batch:
            if isinstance(item, torch.Tensor):
                single_batch.append(item[batch_idx:batch_idx+1])
            elif isinstance(item, list):
                single_batch.append([item[batch_idx]] if batch_idx < len(item) else [[]])
            else:
                single_batch.append(item)
        return single_batch
    
    def _score_candidate_quality(self, sequence):
        """评估候选序列质量"""
        tokens = [self.idx2token.get(idx.item(), '<unk>') for idx in sequence]
        pairs = self.post_processor.process(tokens)
        
        if len(pairs) == 0:
            return -1.0
        
        # 基础分数：鼓励更多的对（提高召回率）
        base_score = min(len(pairs), self.max_pairs_per_sample) * 1.0
        
        # 质量分数
        quality_score = 0.0
        for speaker, emotion, cause in pairs:
            if speaker in self.valid_speakers:
                quality_score += 0.3
            if emotion in self.valid_emotions:
                quality_score += 0.4
            if cause in self.valid_speakers:
                quality_score += 0.2
        
        # 结构分数
        sep_count = tokens.count('<sep>')
        has_eos = '<eos>' in tokens
        structure_score = sep_count * 0.1 + (0.2 if has_eos else -0.1)
        
        return base_score + quality_score + structure_score
    
    def _calculate_diversity_score(self, candidate, all_candidates, current_method):
        """计算多样性分数"""
        if len(all_candidates) <= 1:
            return 0.0
        
        diversity_score = 0.0
        candidate_tokens = [self.idx2token.get(idx.item(), '<unk>') for idx in candidate]
        candidate_pairs = set(self.post_processor.process(candidate_tokens))

        for method, other_candidate in all_candidates:
            if method == current_method:
                continue

            other_tokens = [self.idx2token.get(idx.item(), '<unk>') for idx in other_candidate]
            other_pairs = set(self.post_processor.process(other_tokens))
            
            if len(candidate_pairs) > 0 and len(other_pairs) > 0:
                overlap = len(candidate_pairs & other_pairs)
                total = len(candidate_pairs | other_pairs)
                similarity = overlap / total if total > 0 else 0
                diversity_score += (1 - similarity)
        
        return diversity_score / max(1, len(all_candidates) - 1)


def enhanced_loss_function(logits, targets, pad_idx=0, smoothing=0.1):
    """增强的损失函数"""
    batch_size, seq_len, vocab_size = logits.shape

    logits_flat = logits.view(-1, vocab_size)
    targets_flat = targets.view(-1)

    mask = targets_flat != pad_idx
    if mask.sum() == 0:
        return torch.tensor(0.0, device=logits.device, requires_grad=True)

    logits_flat = logits_flat[mask]
    targets_flat = targets_flat[mask]

    # 基础交叉熵损失
    base_loss = nn.CrossEntropyLoss()(logits_flat, targets_flat)

    # 多样性损失（鼓励生成多样化的token）
    probs = torch.softmax(logits_flat, dim=-1)
    entropy = -(probs * torch.log(probs + 1e-8)).sum(dim=-1).mean()
    diversity_loss = -entropy * 0.03  # 适中的多样性权重

    # 结构损失（关注重要token）
    structure_loss = 0.0
    sep_token_id = 3
    eos_token_id = 2

    important_tokens = [sep_token_id, eos_token_id]
    for token_id in important_tokens:
        token_mask = (targets_flat == token_id)
        if token_mask.any():
            token_logits = logits_flat[token_mask]
            token_targets = targets_flat[token_mask]
            token_loss = nn.CrossEntropyLoss()(token_logits, token_targets)
            structure_loss += token_loss * 0.2

    total_loss = base_loss + diversity_loss + structure_loss
    return total_loss


def analyze_prediction_quality(predictions, targets, idx2token):
    """分析预测质量"""
    analysis_results = {
        'total_samples': len(predictions),
        'duplicate_pairs': 0,
        'consecutive_same_emotion': 0,
        'invalid_speakers': 0,
        'invalid_emotions': 0,
        'avg_pairs_per_sample': 0,
        'diversity_score': 0,
        'high_quality_samples': 0  # 新增：高质量样本数
    }

    valid_speakers = {'Chandler', 'Joey', 'Ross', 'Rachel', 'Monica', 'Phoebe', '_NONE'}
    valid_emotions = {'surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear'}

    # 创建后处理器
    post_processor = UnifiedPostProcessor(mode='optimal')

    all_emotions = []
    all_speakers = []
    total_pairs = 0

    for i in range(len(predictions)):
        pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in predictions[i]]
        pred_pairs = post_processor.process(pred_tokens)

        total_pairs += len(pred_pairs)

        # 检查重复对
        unique_pairs = set(pred_pairs)
        if len(unique_pairs) < len(pred_pairs):
            analysis_results['duplicate_pairs'] += 1

        # 检查连续相同情感
        emotions = [pair[1] for pair in pred_pairs]
        consecutive_count = 0
        for j in range(len(emotions) - 2):
            if emotions[j] == emotions[j+1] == emotions[j+2]:
                consecutive_count += 1
        if consecutive_count > 0:
            analysis_results['consecutive_same_emotion'] += 1

        # 检查无效说话者和情感
        invalid_count = 0
        for speaker, emotion, cause in pred_pairs:
            if speaker not in valid_speakers:
                analysis_results['invalid_speakers'] += 1
                invalid_count += 1
            if emotion not in valid_emotions:
                analysis_results['invalid_emotions'] += 1
                invalid_count += 1

            all_emotions.append(emotion)
            all_speakers.append(speaker)

        # 高质量样本：没有重复、没有连续相同情感、没有无效token
        if (len(unique_pairs) == len(pred_pairs) and
            consecutive_count == 0 and
            invalid_count == 0 and
            len(pred_pairs) > 0):
            analysis_results['high_quality_samples'] += 1

    # 计算平均对数
    analysis_results['avg_pairs_per_sample'] = total_pairs / len(predictions) if len(predictions) > 0 else 0

    # 计算多样性分数
    emotion_diversity = len(set(all_emotions)) / len(all_emotions) if all_emotions else 0
    speaker_diversity = len(set(all_speakers)) / len(all_speakers) if all_speakers else 0
    analysis_results['diversity_score'] = (emotion_diversity + speaker_diversity) / 2

    return analysis_results


def train_hybrid_model(args=None, config=None):
    """训练混合束搜索模型"""
    # 如果没有提供参数，解析命令行参数
    if args is None or config is None:
        args, config = parse_args_and_create_config()
        set_config(config)

    print(f"开始混合束搜索训练... (数据集: {config.dataset.name})")

    # 设置随机种子
    torch.manual_seed(config.experiment.seed)
    np.random.seed(config.experiment.seed)
    random.seed(config.experiment.seed)

    # 打印配置摘要
    print_args(args)
    config.print_config()

    # 创建数据集
    train_dataset = DialogDataset(
        split="train",
        data_path=config.dataset.get_data_path(),
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers(),
        window_past=config.model.window_past,
        window_future=config.model.window_future
    )
    val_dataset = DialogDataset(
        split="test",  # 使用test作为验证集
        data_path=config.dataset.get_data_path(),
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers(),
        window_past=config.model.window_past,
        window_future=config.model.window_future
    )

    # 创建词汇表 - 使用话语ID格式
    token2idx, idx2token = create_ecpe_vocab(
        emotion_categories=config.dataset.get_emotion_categories(),
        max_utterances=100  # 支持最多100个话语
    )

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=config.training.batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=config.training.batch_size, shuffle=False, collate_fn=collate_fn)

    # 创建模型 - 设置vocab_size到配置中
    config.model.vocab_size = len(token2idx)
    model = Graph2SeqECPE(config.model)

    # 显示增强功能配置
    print(f"\n增强功能配置:")
    print(f"  邻居采样: {config.model.use_neighbor_sampling}")
    print(f"  采样大小: {config.model.neighbor_sample_size}")
    print(f"  采样策略: {config.model.sampling_strategy}")
    print(f"  门控聚合器: {config.model.use_gated_aggregator}")
    print(f"  聚合器类型: {config.model.aggregator_type}")
    print(f"  图编码器类型: {config.model.graph_encoder_type}")
    print(f"  后处理模式: {config.model.post_processing_mode}")

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config.training.device != 'cpu' else 'cpu')
    model = model.to(device)

    print(f"使用设备: {device}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 创建混合束搜索解码器（优化参数以提高召回率）
    hybrid_decoder = HybridBeamSearchDecoder(
        model=model,
        token2idx=token2idx,
        idx2token=idx2token,
        beam_size=config.model.beam_size,
        max_length=config.model.max_decode_length,
        confidence_threshold=0.3,  # 默认置信度阈值
        diversity_weight=0.5,  # 默认多样性权重
        max_pairs_per_sample=10  # 默认最大对数
    )

    # 优化器设置
    optimizer = optim.AdamW([
        {'params': model.plm.parameters(), 'lr': config.training.learning_rate * 0.1},  # PLM使用较小学习率
        {'params': [p for n, p in model.named_parameters() if 'plm' not in n], 'lr': config.training.learning_rate}
    ], weight_decay=config.training.weight_decay)

    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=5, T_mult=2)

    # 创建改进的损失函数
    print(f"\n🔧 创建改进的损失函数...")

    if config.training.loss_type == 'cross_entropy':
        # 使用标准交叉熵损失
        loss_function = lambda logits, targets: compute_loss(logits, targets, pad_idx=token2idx.get('<pad>', 0))
        print(f"✅ 使用标准交叉熵损失")
    elif config.training.loss_type == 'label_smoothing':
        # 只使用标签平滑，更稳定
        loss_function = create_advanced_loss_function(
            loss_type='label_smoothing',
            token2idx=token2idx,
            smoothing=config.training.label_smoothing,
            ignore_index=token2idx.get('<pad>', 0)
        )
        print(f"✅ 使用标签平滑损失 (smoothing={config.training.label_smoothing})")
    else:
        # 使用改进的损失函数，但降低权重
        loss_function = create_advanced_loss_function(
            loss_type=config.training.loss_type,
            token2idx=token2idx,
            smoothing=config.training.label_smoothing,
            ignore_index=token2idx.get('<pad>', 0),
            alpha=config.training.focal_alpha,
            gamma=config.training.focal_gamma,
            alpha_structure=0.25,  # 降低权重
            alpha_diversity=0.25,  # 降低权重
            alpha_consistency=0.25,  # 降低权重
            weight_smoothing=0.8,  # 增加基础损失权重
            weight_ecpe=0.2  # 降低ECPE特定损失权重
        )

        print(f"✅ 损失函数配置:")
        print(f"  类型: {config.training.loss_type}")
        if config.training.loss_type in ['label_smoothing', 'combined']:
            print(f"  标签平滑: {config.training.label_smoothing}")
        if config.training.loss_type in ['focal', 'combined']:
            print(f"  Focal Loss alpha: {config.training.focal_alpha}")
            print(f"  Focal Loss gamma: {config.training.focal_gamma}")
        if config.training.loss_type in ['ecpe_specific', 'combined']:
            print(f"  结构损失权重: 0.25")
            print(f"  多样性损失权重: 0.25")
            print(f"  一致性损失权重: 0.25")

    # 创建自适应损失调度器（仅对高级损失函数）
    if config.training.loss_type != 'cross_entropy':
        loss_scheduler = AdaptiveLossScheduler(total_epochs=config.training.epochs)
    else:
        loss_scheduler = None

    best_f1 = 0.0
    best_precision = 0.0
    best_recall = 0.0
    # 保存最佳F1对应的完整指标
    best_f1_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    patience_counter = 0
    max_patience = config.training.patience

    # 改进的早停策略
    f1_history = []  # 记录F1历史
    consecutive_drops = 0  # 连续下降次数
    drop_threshold = 0.01  # F1下降阈值
    max_consecutive_drops = 3  # 最大连续下降次数

    print(f"✅ 改进的早停策略:")
    print(f"  传统patience: {max_patience}")
    print(f"  F1下降阈值: {drop_threshold}")
    print(f"  最大连续下降: {max_consecutive_drops}")
    print(f"  PLM分层解冻: 禁用")  # 简化配置，默认禁用

    # 分阶段训练 - 修改为从第1个epoch就开始使用混合束搜索（启用后处理）
    use_hybrid_search_epoch = 1  # 从第1个epoch开始使用混合束搜索和后处理

    # PLM分层解冻策略
    def apply_layered_unfreezing(model, epoch, total_epochs):
        """
        分层解冻PLM：逐步解冻顶层
        """
        if not hasattr(model, 'plm') or not hasattr(model.plm, 'encoder'):
            return

        # 获取编码器层数
        if hasattr(model.plm.encoder, 'layer'):
            total_layers = len(model.plm.encoder.layer)
        elif hasattr(model.plm, 'encoder') and hasattr(model.plm.encoder, 'layers'):
            total_layers = len(model.plm.encoder.layers)
        else:
            print(f"  ⚠️ 无法获取PLM层数，跳过分层解冻")
            return 0

        # 计算解冻进度
        unfreeze_progress = epoch / total_epochs

        # 分阶段解冻策略
        if epoch <= 3:
            # 前3个epoch：只解冻最后2层
            layers_to_unfreeze = max(2, int(total_layers * 0.2))
        elif epoch <= 8:
            # 4-8个epoch：解冻最后40%的层
            layers_to_unfreeze = max(3, int(total_layers * 0.4))
        elif epoch <= 15:
            # 9-15个epoch：解冻最后60%的层
            layers_to_unfreeze = max(4, int(total_layers * 0.6))
        else:
            # 15个epoch后：解冻最后80%的层
            layers_to_unfreeze = max(6, int(total_layers * 0.8))

        # 冻结所有层
        for param in model.plm.parameters():
            param.requires_grad = False

        # 解冻指定的顶层
        start_layer = total_layers - layers_to_unfreeze
        for i in range(start_layer, total_layers):
            for param in model.plm.encoder.layer[i].parameters():
                param.requires_grad = True

        # 始终解冻pooler（如果存在）
        if hasattr(model.plm, 'pooler') and model.plm.pooler is not None:
            for param in model.plm.pooler.parameters():
                param.requires_grad = True

        print(f"  PLM分层解冻: 解冻最后 {layers_to_unfreeze}/{total_layers} 层")

        return layers_to_unfreeze

    for epoch in range(config.training.epochs):
        print(f"\nEpoch {epoch + 1}/{config.training.epochs}")

        # 更新损失调度器（如果使用高级损失函数）
        if loss_scheduler is not None:
            loss_scheduler.step(epoch)

        # PLM分层解冻（简化配置，默认禁用）
        # if use_layered_unfreezing:
        #     unfrozen_layers = apply_layered_unfreezing(model, epoch + 1, config.training.epochs)

        # 动态调整teacher forcing
        teacher_forcing_ratio = max(0.2, 0.95 - epoch * 0.06)
        print(f"Teacher forcing ratio: {teacher_forcing_ratio:.2f}")

        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0

        for batch_idx, batch in enumerate(train_loader):
                target_seqs = prepare_target_sequences(batch, token2idx, max_length=20)
                target_seqs = target_seqs.to(device)

                if target_seqs.sum() == 0:
                    continue

                optimizer.zero_grad()
                decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)

                # 使用改进的损失函数
                loss = loss_function(decoder_outputs, target_seqs)

                # 检查损失
                if torch.isnan(loss) or loss.item() > 15.0:
                    print(f"  警告：异常损失值 {loss.item():.4f}，跳过此批次")
                    continue

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()

                train_loss += loss.item()
                train_batches += 1


        scheduler.step()
        avg_train_loss = train_loss / max(1, train_batches)
        print(f"平均训练损失: {avg_train_loss:.4f}")

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_batches = 0
        all_predictions = []
        all_targets = []

        # 决定是否使用混合束搜索
        use_hybrid_search = epoch >= use_hybrid_search_epoch
        if use_hybrid_search:
            print("使用混合束搜索进行预测")

        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):

                try:
                    target_seqs = prepare_target_sequences(batch, token2idx, max_length=20)
                    target_seqs = target_seqs.to(device)

                    # 计算损失
                    decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                    loss = loss_function(decoder_outputs, target_seqs)
                    val_loss += loss.item()
                    val_batches += 1

                    # 预测
                    if use_hybrid_search:
                        predictions = hybrid_decoder.hybrid_beam_search(batch)
                    else:
                        predictions = model.predict(batch, max_length=20)
                        # 为普通预测也应用后处理
                        processed_predictions = []
                        for i in range(predictions.size(0)):
                            filtered_pred = hybrid_decoder._advanced_post_process_filter(predictions[i])
                            processed_predictions.append(filtered_pred)

                        # 填充序列到相同长度
                        if processed_predictions:
                            max_len = max(len(pred) for pred in processed_predictions)
                            padded_predictions = []
                            for pred in processed_predictions:
                                if len(pred) < max_len:
                                    # 使用PAD token填充
                                    pad_length = max_len - len(pred)
                                    padded_pred = torch.cat([pred, torch.full((pad_length,), hybrid_decoder.pad_id, device=pred.device)])
                                else:
                                    padded_pred = pred
                                padded_predictions.append(padded_pred)
                            predictions = torch.stack(padded_predictions)
                        else:
                            # 如果没有处理结果，保持原始预测
                            pass

                    all_predictions.append(predictions)
                    all_targets.append(target_seqs)

                except Exception as e:
                    print(f"验证批次 {batch_idx} 出错: {e}")
                    continue

        avg_val_loss = val_loss / max(1, val_batches)

        # 计算指标
        if all_predictions and all_targets:
            combined_predictions = torch.cat(all_predictions, dim=0)
            combined_targets = torch.cat(all_targets, dim=0)
            metrics = compute_ecpe_metrics(combined_predictions, combined_targets, idx2token)

            # 分析预测质量
            if use_hybrid_search:
                quality_analysis = analyze_prediction_quality(combined_predictions, combined_targets, idx2token)
                print(f"预测质量分析:")
                print(f"  平均每样本对数: {quality_analysis['avg_pairs_per_sample']:.2f}")
                print(f"  重复对样本数: {quality_analysis['duplicate_pairs']}")
                print(f"  连续相同情感样本数: {quality_analysis['consecutive_same_emotion']}")
                print(f"  高质量样本数: {quality_analysis['high_quality_samples']}/{quality_analysis['total_samples']}")
                print(f"  多样性分数: {quality_analysis['diversity_score']:.3f}")
        else:
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}

        print(f"验证损失: {avg_val_loss:.4f}")
        print(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
        print(f"当前学习率: PLM={optimizer.param_groups[0]['lr']:.6f}, Other={optimizer.param_groups[1]['lr']:.6f}")

        # 获取当前F1分数用于早停策略
        current_f1 = metrics['f1']

        # 保存最佳模型（优先考虑F1，但也关注精确率和召回率的平衡）
        improved = False
        if metrics['f1'] > best_f1:
            best_f1 = metrics['f1']
            # 保存最佳F1对应的完整指标（确保一致性）
            best_f1_metrics = {
                'f1': metrics['f1'],
                'precision': metrics['precision'],
                'recall': metrics['recall']
            }
            improved = True
            print(f"  ✓ 新的最佳F1: {best_f1:.4f}")

        if metrics['precision'] > best_precision:
            best_precision = metrics['precision']
            if not improved and metrics['precision'] > 0.6:  # 精确率阈值
                improved = True
                print(f"  ✓ 新的最佳精确率: {best_precision:.4f}")

        if metrics['recall'] > best_recall:
            best_recall = metrics['recall']
            if not improved and metrics['recall'] > 0.4:  # 召回率阈值
                improved = True
                print(f"  ✓ 新的最佳召回率: {best_recall:.4f}")

        # 更新F1历史和改进的早停策略
        f1_history.append(current_f1)

        # 检查连续F1下降
        if len(f1_history) >= 2:
            f1_drop = f1_history[-2] - f1_history[-1]
            if f1_drop > drop_threshold:
                consecutive_drops += 1
                print(f"  F1下降 {f1_drop:.4f} (连续下降: {consecutive_drops}/{max_consecutive_drops})")
            else:
                consecutive_drops = 0

        if improved:
            patience_counter = 0
            consecutive_drops = 0  # 重置连续下降计数
            # 保存最佳模型
            model_save_path = f"{config.experiment.output_dir}/best_model_{config.dataset.name}.pt"
            torch.save(model.state_dict(), model_save_path)
            print(f"  模型已保存到: {model_save_path}")
        else:
            patience_counter += 1
            print(f"  验证指标未改善 ({patience_counter}/{max_patience})")

        # 改进的早停条件
        early_stop_triggered = False

        # 条件1：传统的patience早停
        if patience_counter >= max_patience:
            print(f"早停触发: 连续{max_patience}次未改善")
            early_stop_triggered = True

        # 条件2：连续F1显著下降早停
        if consecutive_drops >= max_consecutive_drops:
            print(f"早停触发: 连续{max_consecutive_drops}次F1下降超过{drop_threshold:.2f}")
            early_stop_triggered = True

        if early_stop_triggered:
            break

        # 显示样例
        if epoch % config.training.eval_steps == 0 and all_predictions:
            print(f"\n预测样例:")
            # 创建后处理器用于样例显示
            sample_processor = UnifiedPostProcessor(mode='optimal')
            for i in range(min(3, combined_predictions.size(0))):
                true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_targets[i]]
                pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_predictions[i]]

                true_pairs = sample_processor.process(true_tokens)
                pred_pairs = sample_processor.process(pred_tokens)

                print(f"  样例 {i+1}:")
                print(f"    真实: {true_pairs}")
                print(f"    预测: {pred_pairs}")

                matches = len(set(true_pairs) & set(pred_pairs))
                print(f"    匹配: {matches}/{len(true_pairs)} (真实) vs {matches}/{len(pred_pairs)} (预测)")

    print(f"\n混合束搜索训练完成！")
    print(f"最佳F1分数: {best_f1:.4f}")
    print(f"对应精确率: {best_f1_metrics['precision']:.4f}")
    print(f"对应召回率: {best_f1_metrics['recall']:.4f}")
    print(f"\n独立最佳指标:")
    print(f"最佳精确率: {best_precision:.4f}")
    print(f"最佳召回率: {best_recall:.4f}")

    # 返回一致的指标（最佳F1对应的精确率和召回率）
    return best_f1_metrics['f1'], best_f1_metrics['precision'], best_f1_metrics['recall']


if __name__ == "__main__":
    # 解析参数并设置配置
    args, config = parse_args_and_create_config()

    # 训练模型
    final_f1, final_precision, final_recall = train_hybrid_model(args, config)
    print(f"\n🏆 最终结果:")
    print(f"F1分数: {final_f1:.4f}")
    print(f"精确率: {final_precision:.4f}")
    print(f"召回率: {final_recall:.4f}")

    # 混合束搜索
    print(f"混合束搜索: F1={final_f1:.4f}, P={final_precision:.4f}, R={final_recall:.4f}")

    # 评估混合束搜索的效果
    print(f"\n🎯 混合束搜索评估:")
    print(f"最终F1分数: {final_f1:.4f}")
    print(f"最终精确率: {final_precision:.4f}")
    print(f"最终召回率: {final_recall:.4f}")

    # 精确率-召回率平衡评估
    if final_precision >= 0.6 and final_recall >= 0.4:
        print("🎉 成功实现高精确率和高召回率的双重目标！")
    elif final_precision >= 0.5 and final_recall >= 0.35:
        print("✅ 实现了较好的精确率和召回率平衡")
    elif final_f1 >= 0.5:
        print("📈 整体性能表现良好")
    else:
        print("📊 需要进一步调整混合策略")
