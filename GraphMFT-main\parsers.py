import argparse
import os
parser = argparse.ArgumentParser()

parser.add_argument('--no-cuda', action='store_true', default=False, help='does not use GPU')
parser.add_argument('--base_model', nargs='+', type=str, default=['None','None','LSTM'], help='[_a,_v,_l], LSTM/GRU/Transformer/Dens/None')
parser.add_argument('--graph_model', action='store_true', default=True, help='whether to use graph model after recurrent encoding')
parser.add_argument('--windowp', type=int, default=16, help='')
parser.add_argument('--windowf', type=int, default=16, help='')
parser.add_argument('--base_nlayers', nargs='+', type=int, default=[2,2,2], help='[base_nlayers_a, base_nlayers_v, base_nlayers_l]')
parser.add_argument('--unimodal_nlayers', nargs='+', type=int, default=[1,1,1,1], help='[unimodal_nlayers_a, unimodal_nlayers_v, unimodal_nlayers_l]')
parser.add_argument('--multimodal_nlayers', nargs='+', type=int, default=[5,5,5], help='[multimodal_nlayers_va,multimodal_nlayers_va_l,multimodal_nlayers_va_l_share]')
parser.add_argument('--norm_first', nargs='+', type=bool, default=[False,False], help='whether norm first or not, [unimodal,multimodal]')
parser.add_argument('--base_size', nargs='+', type=int, default=[400,400,400], help='base_size, control base model input, a,v,l')
parser.add_argument('--hidesize', type=int, default=200, help='graphnet: list of t_hidesize')
parser.add_argument('--list_mlp', nargs='+', type=int, default=[], help='classifier layers')
parser.add_argument('--nheads', type=int, default=4, help='graph_nheads')
parser.add_argument('--lr', type=float, default=0.00001, metavar='LR', help='learning rate')
parser.add_argument('--l2', type=float, default=0.00001, metavar='L2', help='L2 regularization weight')
parser.add_argument('--dropout', type=float, default=0.5, metavar='dropout', help='dropout rate')
parser.add_argument('--batch_size', type=int, default=16, metavar='BS', help='batch size')
parser.add_argument('--epochs', type=int, default=150, metavar='E', help='number of epochs')
parser.add_argument('--tensorboard', action='store_true', default=False, help='Enables tensorboard log')
parser.add_argument('--multi_modal', action='store_true', default=True, help='whether to use multimodal information')
parser.add_argument('--fusion_method', default='concat', help='method to fusion: sum/concat/film/gated')
parser.add_argument('--modals', default='avl', help='modals')
parser.add_argument('--Dataset', default='IEMOCAP', help='dataset to train and test.MELD/IEMOCAP')
parser.add_argument('--ratio_speaker', type=float, default=1.0, metavar='speaker', help='rate of speaker embedding') 
parser.add_argument('--ratio_modal', type=float, default=0.0, metavar='modal embedding', help='whether to use modal embedding')
parser.add_argument('--use_residual', action='store_true', default=True, help='gra_residual')
parser.add_argument('--class_weight', action='store_true', default=True, help='use class weights')

args = parser.parse_args()