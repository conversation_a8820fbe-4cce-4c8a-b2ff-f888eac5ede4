#!/usr/bin/env python3
"""
简化版训练脚本 - 使用贪婪解码而不是复杂的束搜索
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random

from config import UnifiedConfig, get_config, set_config
from data import DialogDataset, collate_fn
from models import Graph2SeqECPE, create_ecpe_vocab
from utils import prepare_target_sequences, compute_loss, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)

def simple_decode(model, batch, token2idx, idx2token, max_length=20, device='cuda'):
    """简单的贪婪解码"""
    model.eval()

    with torch.no_grad():
        # 使用模型的predict方法
        predictions = model.predict(batch, max_length=max_length)

        # 转换为token序列
        generated_sequences = []
        for pred in predictions:
            tokens = [idx2token.get(idx.item() if hasattr(idx, 'item') else idx, '<unk>') for idx in pred]
            generated_sequences.append(tokens)

    return generated_sequences

def train_epoch(model, dataloader, optimizer, token2idx, idx2token, device, teacher_forcing_ratio=0.8):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    num_batches = 0
    
    for batch in tqdm(dataloader, desc="训练"):
        optimizer.zero_grad()
        
        # 准备目标序列 - 使用话语ID格式
        target_sequences = prepare_target_sequences(batch, token2idx, max_length=20)
        target_sequences = target_sequences.to(device)
        
        # 前向传播
        logits, attention_weights = model(batch, target_sequences, teacher_forcing_ratio=teacher_forcing_ratio)

        # 计算损失
        loss = compute_loss(logits, target_sequences, pad_idx=token2idx['<pad>'])
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        num_batches += 1
    
    return total_loss / num_batches

def evaluate(model, dataloader, token2idx, idx2token, device):
    """评估模型"""
    model.eval()
    total_loss = 0
    all_predictions = []
    all_targets = []

    # 创建统一后处理器 (使用optimal模式获得最佳性能)
    post_processor = UnifiedPostProcessor(mode='optimal')

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="评估"):
            # 准备目标序列 - 使用话语ID格式
            target_sequences = prepare_target_sequences(batch, token2idx, max_length=20)
            target_sequences = target_sequences.to(device)

            # 计算损失
            logits, _ = model(batch, target_sequences, teacher_forcing_ratio=0.0)  # 不使用teacher forcing
            loss = compute_loss(logits, target_sequences, pad_idx=token2idx['<pad>'])
            total_loss += loss.item()

            # 生成预测
            predictions = simple_decode(model, batch, token2idx, idx2token, device=device)

            # 转换目标序列为tokens
            for i in range(target_sequences.size(0)):
                target_tokens = [idx2token.get(idx.item(), '<unk>') for idx in target_sequences[i]]
                all_targets.append(target_tokens)
                if i < len(predictions):
                    all_predictions.append(predictions[i])
                else:
                    all_predictions.append(['<sos>', '<eos>'])

    # 计算指标 - 使用统一后处理器
    all_pred_pairs = []
    all_true_pairs = []

    for pred_tokens, true_tokens in zip(all_predictions, all_targets):
        pred_pairs = post_processor.process(pred_tokens)
        true_pairs = post_processor.process(true_tokens)
        all_pred_pairs.extend(pred_pairs)
        all_true_pairs.extend(true_pairs)
    
    # 计算精确率、召回率、F1
    pred_set = set(all_pred_pairs)
    true_set = set(all_true_pairs)
    
    if len(pred_set) == 0 and len(true_set) == 0:
        precision = recall = f1 = 1.0
    elif len(pred_set) == 0:
        precision = recall = f1 = 0.0
    elif len(true_set) == 0:
        precision = recall = f1 = 0.0
    else:
        intersection = pred_set & true_set
        precision = len(intersection) / len(pred_set) if len(pred_set) > 0 else 0.0
        recall = len(intersection) / len(true_set) if len(true_set) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    
    avg_loss = total_loss / len(dataloader)
    
    return avg_loss, precision, recall, f1, all_predictions[:3], all_targets[:3]

def main():
    # 解析参数并创建配置
    args, config = parse_args_and_create_config()

    # 设置全局配置
    set_config(config)

    # 设置随机种子
    set_seed(config.experiment.seed)

    # 打印配置信息
    print_args(args)
    config.print_config()

    print(f"开始简化训练... (数据集: {config.dataset.name})")

    # 创建词汇表 - 使用话语ID格式
    token2idx, idx2token = create_ecpe_vocab(
        emotion_categories=config.dataset.get_emotion_categories(),
        max_utterances=100  # 支持最多100个话语
    )
    vocab_size = len(token2idx)

    print(f"词汇表大小: {vocab_size}")
    print(f"使用话语ID格式，支持最多100个话语")
    
    print(f"词汇表大小: {vocab_size}")
    
    # 创建数据集
    train_dataset = DialogDataset(
        split="train",
        data_path=config.dataset.get_data_path(),
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers(),
        window_past=config.model.window_past,
        window_future=config.model.window_future
    )
    val_dataset = DialogDataset(
        split="test",  # 使用test作为验证集
        data_path=config.dataset.get_data_path(),
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers(),
        window_past=config.model.window_past,
        window_future=config.model.window_future
    )

    train_loader = DataLoader(train_dataset, batch_size=config.training.batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=config.training.batch_size, shuffle=False, collate_fn=collate_fn)
    
    # 创建模型 - 设置vocab_size到配置中
    config.model.vocab_size = vocab_size
    model = Graph2SeqECPE(config.model)

    device = torch.device('cuda' if torch.cuda.is_available() and config.training.device != 'cpu' else 'cpu')
    model = model.to(device)
    
    print(f"使用设备: {device}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建优化器
    plm_params = []
    other_params = []
    
    for name, param in model.named_parameters():
        if 'plm_model' in name:
            plm_params.append(param)
        else:
            other_params.append(param)
    
    optimizer = torch.optim.AdamW([
        {'params': plm_params, 'lr': config.training.learning_rate * 0.1},  # PLM使用较小学习率
        {'params': other_params, 'lr': config.training.learning_rate}
    ], weight_decay=config.training.weight_decay)
    
    # 训练循环
    best_f1 = 0.0
    patience_counter = 0
    
    for epoch in range(config.training.epochs):
        print(f"\nEpoch {epoch+1}/{config.training.epochs}")
        
        # 动态调整teacher forcing ratio
        teacher_forcing_ratio = max(0.5, 1.0 - epoch * 0.1)
        print(f"Teacher forcing ratio: {teacher_forcing_ratio:.2f}")
        
        # 训练
        train_loss = train_epoch(model, train_loader, optimizer, token2idx, idx2token, device, teacher_forcing_ratio)
        
        # 评估
        val_loss, precision, recall, f1, sample_preds, sample_targets = evaluate(model, val_loader, token2idx, idx2token, device)
        
        print(f"训练损失: {train_loss:.4f}")
        print(f"验证损失: {val_loss:.4f}")
        print(f"验证指标: P={precision:.4f}, R={recall:.4f}, F1={f1:.4f}")
        
        # 显示预测样例
        if epoch % 2 == 0:
            print("预测样例:")
            # 创建后处理器用于样例显示
            sample_processor = UnifiedPostProcessor(mode='optimal')
            for i, (pred, target) in enumerate(zip(sample_preds, sample_targets)):
                pred_pairs = sample_processor.process(pred)
                target_pairs = sample_processor.process(target)
                print(f"  样例 {i+1}:")
                print(f"    真实: {target_pairs}")
                print(f"    预测: {pred_pairs}")
        
        # 早停检查
        if f1 > best_f1:
            best_f1 = f1
            patience_counter = 0
            # 保存最佳模型
            model_save_path = f"{config.experiment.output_dir}/best_model.pt"
            torch.save(model.state_dict(), model_save_path)
            print(f"  保存最佳模型 (F1={f1:.4f})")
        else:
            patience_counter += 1
            print(f"  验证指标未改善 ({patience_counter}/{config.training.patience})")

            if config.training.early_stopping and patience_counter >= config.training.patience:
                print("早停触发")
                break
    
    print(f"\n训练完成！最佳F1分数: {best_f1:.4f}")

if __name__ == "__main__":
    main()
