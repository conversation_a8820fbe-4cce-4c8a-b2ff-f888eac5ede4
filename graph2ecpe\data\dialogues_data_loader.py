import os
import json
import jsonpickle
from utils.EmotionCauseLink import EmotionCauseLink
from utils.UtteranceItem import UtteranceItem
import utils


def load_dialogues_json_data(dataset_type="train", data_path=None):
    """
    Load dialogue data from JSON files with improved emotion-cause pair parsing

    Args:
        dataset_type: Type of dataset to load ("train", "test", etc.)
        data_path: Path to the data directory (if None, uses default)

    Returns:
        nodes: Dictionary of utterance nodes with emotion-cause links
    """
    if data_path is None:
        # 默认数据路径
        data_path = "data/meld/"

    filepath = os.path.join(data_path, dataset_type + ".json")
    with open(filepath, mode="r", encoding="utf8") as file:
        data = jsonpickle.decode(file.read())

    nodes = {}
    for dialog in data:
        conversation_id = dialog["conversation_ID"]
        emotion_cause_pairs = dialog["emotion-cause_pairs"]
        conversation = dialog["conversation"]

        # Create a mapping from utterance_ID to utterance for quick lookup
        utterance_map = {utt["utterance_ID"]: utt for utt in conversation}

        for utterance in conversation:
            utt_node = UtteranceItem(
                utterance["utterance_ID"],
                utterance["text"],
                utterance["emotion"],
                utterance["speaker"]
            )

            # Process emotion-cause pairs with improved validation
            for emotion_cause_pair in emotion_cause_pairs:
                try:
                    # Parse emotion utterance (format: "utterance_id_emotion")
                    emotion_part = emotion_cause_pair[0]
                    cause_part = emotion_cause_pair[1]

                    # Extract emotion utterance ID and emotion type
                    emotion_parts = emotion_part.split("_")
                    if len(emotion_parts) >= 2:
                        emotion_utt_id = int(emotion_parts[0])
                        emotion_type = "_".join(emotion_parts[1:])  # Handle multi-word emotions
                    else:
                        continue  # Skip malformed pairs

                    # Extract cause utterance ID (format: "utterance_id_text" or just text)
                    cause_parts = cause_part.split("_")
                    if len(cause_parts) >= 1 and cause_parts[0].isdigit():
                        cause_utt_id = int(cause_parts[0])
                    else:
                        # If cause_part doesn't start with digit, try to find matching utterance by text
                        cause_utt_id = None
                        cause_text = cause_part
                        for utt_id, utt_data in utterance_map.items():
                            if cause_text in utt_data["text"]:
                                cause_utt_id = utt_id
                                break
                        if cause_utt_id is None:
                            continue  # Skip if can't find matching utterance

                    # Validate that this utterance is the emotion holder
                    if utterance["utterance_ID"] == emotion_utt_id:
                        # Additional validation: check if emotion matches
                        if utterance["emotion"] == emotion_type or emotion_type in utterance["emotion"]:
                            emotion_cause_link = EmotionCauseLink(
                                emotion_utt_id,
                                cause_utt_id,
                                emotion_type
                            )
                            utt_node.append_emotion_cause_link(emotion_cause_link)
                        else:
                            print(f"Warning: Emotion mismatch for utterance {emotion_utt_id}: "
                                  f"expected {emotion_type}, got {utterance['emotion']}")

                except (ValueError, IndexError) as e:
                    print(f"Error parsing emotion-cause pair {emotion_cause_pair}: {e}")
                    continue

            node_id = str(conversation_id) + "_" + str(utterance["utterance_ID"])
            if node_id not in nodes:
                nodes[node_id] = utt_node
            else:
                print(f"Warning: Duplicate node_id {node_id}")

    return nodes


def prepare_eval_data(output_filename, eval_data_path="data/meld/eval.json"):
    # load all data
    # Opening JSON file
    f = open(eval_data_path)

    # returns JSON object as
    # a dictionary
    data = json.load(f)

    # Closing file
    f.close()

    eval_nodes = {}
    # create a graph from data --> utterances are nodes, emotion-cause pairs = an edge between two nodes
    for item in data:
        conversation = item["conversation"]
        for utt in conversation:
            # node_id format = <CONVERSATION_ID>_<UTTERANCE_ID>
            node_id = str(item['conversation_ID'])+"_"+str(utt['utterance_ID'])
            utt_node = UtteranceItem(node_id, utt['text'], "", utt['speaker'], utt["video_name"])
            if node_id not in eval_nodes:
                eval_nodes[node_id] = utt_node
            else:
                print(f"Error: duplicite utterance id {node_id}")

    # output_filename = os.path.join(config.train_test_dev_splits_path, "eval.json")
    utils.create_json_representation_file(eval_nodes, output_filename=output_filename)


