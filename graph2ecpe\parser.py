#!/usr/bin/env python3
"""
统一参数解析器
为所有训练脚本提供统一的命令行参数解析
"""

import argparse
from typing import Optional
from config import UnifiedConfig


def create_parser() -> argparse.ArgumentParser:
    """
    创建统一的参数解析器
    
    Returns:
        argparse.ArgumentParser: 配置好的参数解析器
    """
    parser = argparse.ArgumentParser(
        description='Graph2Sequence ECPE Training - 统一参数解析器',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # ===== 数据集相关参数 =====
    data_group = parser.add_argument_group('数据集配置')
    data_group.add_argument('--dataset', 
                           type=str, 
                           default='meld',
                           choices=['meld', 'dailydialog', 'iemocap'],
                           help='选择数据集')
    
    # ===== 模型相关参数 =====
    model_group = parser.add_argument_group('模型配置')
    
    # 预训练语言模型
    model_group.add_argument('--plm_model',
                            type=str,
                            default='roberta',
                            choices=['roberta', 'bert', 'electra'],
                            help='预训练语言模型类型')
    
    model_group.add_argument('--plm_path',
                            type=str,
                            default='/userdata2/fengweijie/roberta',
                            help='预训练语言模型路径')
    
    # 模型架构
    model_group.add_argument('--hidden_size',
                            type=int,
                            default=256,
                            help='隐藏层大小')
    
    model_group.add_argument('--gnn_layers',
                            type=int,
                            default=2,
                            help='GNN层数')
    
    model_group.add_argument('--decoder_layers',
                            type=int,
                            default=1,
                            help='解码器层数')
    
    model_group.add_argument('--num_attention_heads',
                            type=int,
                            default=8,
                            help='注意力头数')
    
    model_group.add_argument('--dropout',
                            type=float,
                            default=0.2,
                            help='Dropout率')
    
    # 图编码器配置
    model_group.add_argument('--graph_encoder_type',
                            type=str,
                            default='gat',
                            choices=['gnn', 'gat'],
                            help='图编码器类型')
    
    model_group.add_argument('--gat_layers',
                            type=int,
                            default=3,
                            help='GAT层数')
    
    model_group.add_argument('--gat_heads',
                            type=int,
                            default=4,
                            help='GAT注意力头数')
    
    # 图构建配置
    model_group.add_argument('--window_past',
                            type=int,
                            default=3,
                            help='过去窗口大小')
    
    model_group.add_argument('--window_future',
                            type=int,
                            default=3,
                            help='未来窗口大小')
    
    model_group.add_argument('--max_utterance_length',
                            type=int,
                            default=128,
                            help='最大话语长度')
    
    # 邻居采样配置
    model_group.add_argument('--use_neighbor_sampling',
                            action='store_true',
                            default=True,
                            help='是否使用邻居采样')
    
    model_group.add_argument('--neighbor_sample_size',
                            type=int,
                            default=10,
                            help='邻居采样大小')
    
    model_group.add_argument('--sampling_strategy',
                            type=str,
                            default='uniform',
                            choices=['uniform', 'degree_based', 'attention_based'],
                            help='采样策略')
    
    # 聚合器配置
    model_group.add_argument('--use_gated_aggregator',
                            action='store_true',
                            default=True,
                            help='是否使用门控聚合器')
    
    model_group.add_argument('--aggregator_type',
                            type=str,
                            default='gated',
                            choices=['mean', 'max', 'attention', 'gated'],
                            help='聚合器类型')
    
    # 解码器配置
    model_group.add_argument('--use_beam_search',
                            action='store_true',
                            default=False,
                            help='是否使用束搜索')
    
    model_group.add_argument('--beam_size',
                            type=int,
                            default=5,
                            help='束搜索大小')
    
    model_group.add_argument('--max_decode_length',
                            type=int,
                            default=50,
                            help='最大解码长度')
    
    # 后处理配置
    model_group.add_argument('--post_processing_mode',
                            type=str,
                            default='optimal',
                            choices=['simple', 'optimal', 'enhanced', 'auto'],
                            help='后处理模式')
    
    # ===== 训练相关参数 =====
    training_group = parser.add_argument_group('训练配置')
    
    training_group.add_argument('--batch_size',
                               type=int,
                               default=8,
                               help='批次大小')
    
    training_group.add_argument('--learning_rate',
                               type=float,
                               default=2e-5,
                               help='学习率')
    
    training_group.add_argument('--epochs',
                               type=int,
                               default=20,
                               help='训练轮数')
    
    training_group.add_argument('--warmup_steps',
                               type=int,
                               default=100,
                               help='预热步数')
    
    training_group.add_argument('--weight_decay',
                               type=float,
                               default=0.01,
                               help='权重衰减')
    
    training_group.add_argument('--gradient_clip_norm',
                               type=float,
                               default=1.0,
                               help='梯度裁剪范数')
    
    # 学习率调度
    training_group.add_argument('--lr_scheduler',
                               type=str,
                               default='linear',
                               choices=['linear', 'cosine', 'polynomial'],
                               help='学习率调度器')
    
    # 早停配置
    training_group.add_argument('--early_stopping',
                               action='store_true',
                               default=True,
                               help='是否使用早停')
    
    training_group.add_argument('--patience',
                               type=int,
                               default=5,
                               help='早停耐心值')
    
    # 损失函数配置
    training_group.add_argument('--loss_type',
                               type=str,
                               default='cross_entropy',
                               choices=['cross_entropy', 'focal', 'label_smoothing'],
                               help='损失函数类型')
    
    training_group.add_argument('--label_smoothing',
                               type=float,
                               default=0.1,
                               help='标签平滑参数')
    
    # 验证和保存
    training_group.add_argument('--eval_steps',
                               type=int,
                               default=100,
                               help='评估步数间隔')
    
    training_group.add_argument('--save_steps',
                               type=int,
                               default=500,
                               help='保存步数间隔')
    
    # ===== 实验相关参数 =====
    experiment_group = parser.add_argument_group('实验配置')
    
    experiment_group.add_argument('--experiment_name',
                                 type=str,
                                 default='graph2seq_ecpe',
                                 help='实验名称')
    
    experiment_group.add_argument('--run_name',
                                 type=str,
                                 default=None,
                                 help='运行名称')
    
    experiment_group.add_argument('--output_dir',
                                 type=str,
                                 default='output',
                                 help='输出目录')
    
    experiment_group.add_argument('--seed',
                                 type=int,
                                 default=42,
                                 help='随机种子')
    
    experiment_group.add_argument('--debug',
                                 action='store_true',
                                 default=False,
                                 help='调试模式')
    
    # ===== 配置预设 =====
    preset_group = parser.add_argument_group('配置预设')
    
    preset_group.add_argument('--config_preset',
                             type=str,
                             default='default',
                             choices=['default', 'lightweight', 'performance', 'debug'],
                             help='配置预设')
    
    # ===== 设备配置 =====
    device_group = parser.add_argument_group('设备配置')
    
    device_group.add_argument('--device',
                             type=str,
                             default='cuda',
                             choices=['cuda', 'cpu'],
                             help='计算设备')
    
    device_group.add_argument('--mixed_precision',
                             action='store_true',
                             default=True,
                             help='是否使用混合精度')
    
    device_group.add_argument('--dataloader_num_workers',
                             type=int,
                             default=4,
                             help='数据加载器工作进程数')
    
    return parser


def parse_args_and_create_config(args: Optional[list] = None) -> tuple[argparse.Namespace, UnifiedConfig]:
    """
    解析命令行参数并创建配置

    Args:
        args: 命令行参数列表，如果为None则从sys.argv获取

    Returns:
        tuple: (解析后的参数, 统一配置对象)
    """
    parser = create_parser()
    parsed_args = parser.parse_args(args)

    # 先创建配置对象（应用预设）
    config = UnifiedConfig(
        dataset_name=parsed_args.dataset,
        config_preset=parsed_args.config_preset
    )

    # 然后从命令行参数更新配置（覆盖预设）
    config.update_from_args(parsed_args)

    return parsed_args, config


def print_args(args: argparse.Namespace):
    """
    打印解析后的参数
    
    Args:
        args: 解析后的参数
    """
    print("=" * 60)
    print("📋 命令行参数")
    print("=" * 60)
    
    # 按组织打印参数
    groups = {
        '数据集': ['dataset'],
        '模型': ['plm_model', 'plm_path', 'hidden_size', 'gnn_layers', 'decoder_layers', 
                'graph_encoder_type', 'use_beam_search', 'post_processing_mode'],
        '训练': ['batch_size', 'learning_rate', 'epochs', 'weight_decay', 'early_stopping'],
        '实验': ['experiment_name', 'output_dir', 'seed', 'debug', 'config_preset']
    }
    
    for group_name, param_names in groups.items():
        print(f"\n{group_name}:")
        for param_name in param_names:
            if hasattr(args, param_name):
                value = getattr(args, param_name)
                print(f"  {param_name}: {value}")
    
    print("=" * 60)


if __name__ == "__main__":
    # 测试参数解析器
    args, config = parse_args_and_create_config()
    print_args(args)
    config.print_config()
