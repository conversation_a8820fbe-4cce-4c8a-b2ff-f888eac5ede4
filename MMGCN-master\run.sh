python train.py --base-model 'LSTM' --graph-model --nodal-attention --dropout 0.4 --lr 0.0003 --batch-size 16 --l2 0.00003 --graph_type='MMGCN' --epochs=20 --graph_construct='direct' --multi_modal --mm_fusion_mthd='concat_subsequently' --modals='avl' --Dataset='IEMOCAP' --Deep_GCN_nlayers 4 --class-weight --use_speaker

python train.py --base-model 'LSTM' --graph-model --nodal-attention --dropout 0.4 --lr 0.001 --batch-size 16 --l2 0.0 --graph_type='MMGCN' --epochs=60 --graph_construct='direct' --multi_modal --mm_fusion_mthd='concat_subsequently' --modals='avl' --Dataset='MELD' --Deep_GCN_nlayers 4  --use_speaker