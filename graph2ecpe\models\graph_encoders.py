"""
Graph Encoder of Graph2Seq Architecture for Emotion-Cause Pair Extraction

Date:
    - Updated for sentiment cause pair extraction task
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

from torch_geometric.nn import GCNConv, GATConv, GATv2Conv
from torch_geometric.nn import global_max_pool, global_mean_pool, GlobalAttention

from .conv_layer import GCNConv_BiD
from .neigh_samplers import create_neighbor_sampler
from .aggregators import create_aggregator

# 定义边类型范围常量 - 与dataset.py中保持一致
EDGE_TYPE_EMOTION_CAUSE = 0      # 情感-原因对边
EDGE_TYPE_TEMPORAL_FORWARD = 1   # 时序前向边
EDGE_TYPE_TEMPORAL_BACKWARD = 2  # 时序后向边
EDGE_TYPE_SAME_SPEAKER = 3       # 同说话者边
EDGE_TYPE_DIFF_SPEAKER = 4       # 不同说话者边

# 新增：情感语义边
EDGE_TYPE_SAME_EMOTION = 5       # 相同情感边
EDGE_TYPE_EMOTION_TRANSITION = 6 # 情感转换边
EDGE_TYPE_EMOTION_CONTRAST = 7   # 情感对比边

# 新增：语义相似性边
EDGE_TYPE_SEMANTIC_SIMILAR = 8   # 语义相似边

# 新增：因果依赖边
EDGE_TYPE_CAUSAL_DEPENDENCY = 9  # 因果依赖边

# 新增：对话结构边
EDGE_TYPE_QUESTION_ANSWER = 10   # 问答对边
EDGE_TYPE_RESPONSE_RELATION = 11 # 回应关系边
EDGE_TYPE_TOPIC_SHIFT = 12       # 话题转换边

# 扩展的边类型数量
NUM_EDGE_TYPES = 13


class GNN(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels, num_layers=2, dropout=0.5,
                 use_gdc=True, gnn_mode='bi_gcn', num_edge_types=None, pooling_method='attention',
                 use_neighbor_sampling=True, sample_size=10, sampling_strategy='uniform',
                 use_gated_aggregator=True, aggregator_type='gated_mean'):
        super().__init__()
        self.dropout = dropout
        self.num_layers = num_layers
        self.pooling_method = pooling_method
        self.use_neighbor_sampling = use_neighbor_sampling
        self.use_gated_aggregator = use_gated_aggregator

        # 边类型数量安全处理
        if num_edge_types is None or num_edge_types <= 0:
            num_edge_types = NUM_EDGE_TYPES  # 使用简化的边类型数量
        self.num_edge_types = num_edge_types

        # 邻居采样器
        if use_neighbor_sampling:
            self.neighbor_sampler = create_neighbor_sampler(
                sampler_type=sampling_strategy,
                sample_size=sample_size
            )

        # 节点特征变换
        self.node_transform = torch.nn.Linear(in_channels, hidden_channels)
        
        # 图卷积层
        conv_layers_list = []
        gated_aggregators_list = []

        for i in range(num_layers):
            # 决定层维度
            if i == 0:
                num_in_ch = hidden_channels  # 经过node_transform后的维度
                num_out_ch = hidden_channels
            elif i == num_layers - 1:
                num_in_ch = hidden_channels
                num_out_ch = out_channels
            else:
                num_in_ch = hidden_channels
                num_out_ch = hidden_channels

            # 决定层类型
            if gnn_mode == 'gcn':
                layer = GCNConv(num_in_ch, num_out_ch, cached=False, normalize=not use_gdc)
            elif gnn_mode == 'bi_gcn':
                layer = GCNConv_BiD(num_in_ch, num_out_ch)
            else:
                raise ValueError("Undefined GNN model! (Not implemented yet!!!)")

            conv_layers_list.append(layer)

            # 门控聚合器
            if use_gated_aggregator:
                gated_agg = create_aggregator(
                    aggregator_type=aggregator_type,
                    input_dim=num_out_ch,
                    output_dim=num_out_ch,
                    dropout=dropout
                )
                gated_aggregators_list.append(gated_agg)

        self.conv_layers = torch.nn.ModuleList(conv_layers_list)
        if use_gated_aggregator:
            self.gated_aggregators = torch.nn.ModuleList(gated_aggregators_list)
        
        # 边类型嵌入 - 简化为统一的边类型嵌入
        self.edge_type_embedding = torch.nn.Embedding(
            self.num_edge_types, hidden_channels
        )
        
        # 边权重计算
        self.edge_weight_mlp = torch.nn.Sequential(
            torch.nn.Linear(hidden_channels, hidden_channels // 2),
            torch.nn.ReLU(),
            torch.nn.Linear(hidden_channels // 2, 1)
        )
        
        # 全局图嵌入计算 - 使用注意力池化而非简单的均值/最大池化
        if pooling_method == 'attention':
            self.graph_pooling = GlobalAttention(
                gate_nn=torch.nn.Sequential(
                    torch.nn.Linear(out_channels, out_channels // 2),
                    torch.nn.ReLU(),
                    torch.nn.Linear(out_channels // 2, 1)
                )
            )
        
        # 图嵌入投影层
        self.graph_projection = torch.nn.Linear(out_channels, out_channels)
        
        # 层归一化
        self.layer_norm = torch.nn.LayerNorm(out_channels)

        # 残差连接的投影层（当输入输出维度不同时）
        self.residual_projection = None
        if in_channels != out_channels:
            self.residual_projection = torch.nn.Linear(in_channels, out_channels)

    def forward(self, x, edge_index, edge_type=None, edge_norm=None, batch=None):
        """
        前向传播
        
        Args:
            x: 节点特征 [num_nodes, in_channels]
            edge_index: 边索引 [2, num_edges]
            edge_type: 边类型 [num_edges]
            edge_norm: 边权重 [num_edges]
            batch: 节点到批次的映射 [num_nodes]
            
        Returns:
            node_embeddings: 更新后的节点特征 [num_nodes, out_channels]
            graph_embedding: 全局图嵌入 [batch_size, out_channels]
        """
        # 安全检查
        if x.size(0) == 0 or edge_index.size(1) == 0:
            # 如果没有节点或边，返回空结果
            device = x.device
            empty_nodes = torch.zeros(0, self.conv_layers[-1].out_channels, device=device)
            empty_graph = torch.zeros(1, self.conv_layers[-1].out_channels, device=device)
            return empty_nodes, empty_graph
            
        # 初始节点特征变换
        x = self.node_transform(x)

        # 邻居采样（如果启用）
        if self.use_neighbor_sampling and edge_index.size(1) > 0:
            sampled_edge_index, edge_mask = self.neighbor_sampler.sample_neighbors(
                edge_index, x.size(0)
            )
            # 更新边相关信息
            if edge_type is not None:
                edge_type = edge_type[edge_mask]
            if edge_norm is not None:
                edge_norm = edge_norm[edge_mask]
            edge_index = sampled_edge_index

        # 处理边权重 - 简化的边类型处理
        if edge_type is not None and edge_type.numel() > 0:
            # 确保边类型在有效范围内
            edge_type_clamped = torch.clamp(edge_type, 0, self.num_edge_types - 1)

            # 获取边类型嵌入
            edge_embeddings = self.edge_type_embedding(edge_type_clamped)

            # 计算边权重
            edge_weight = torch.sigmoid(self.edge_weight_mlp(edge_embeddings)).squeeze(-1)

            # 应用额外的边权重
            if edge_norm is not None:
                edge_weight = edge_weight * edge_norm
        else:
            edge_weight = edge_norm
        
        # 应用图卷积层
        residual = x  # 保存残差连接
        for i, conv_layer in enumerate(self.conv_layers):
            if self.training:
                x = F.dropout(x, p=self.dropout, training=self.training)

            # 最后一层不应用激活函数
            if i == self.num_layers - 1:
                x = conv_layer(x, edge_index, edge_weight)
            else:
                x = conv_layer(x, edge_index, edge_weight).relu()

        # 改进的残差连接
        if self.residual_projection is not None:
            # 如果输入输出维度不同，投影残差
            residual = self.residual_projection(residual)

        # 添加残差连接并应用层归一化
        if x.size() == residual.size():
            x = residual + x

        node_embeddings = self.layer_norm(x)
        
        # 计算全局图表示
        if batch is not None and node_embeddings.size(0) > 0:
            # 使用选择的池化方法
            if self.pooling_method == 'max':
                pooled = global_max_pool(node_embeddings, batch)
            elif self.pooling_method == 'mean':
                pooled = global_mean_pool(node_embeddings, batch)
            elif self.pooling_method == 'attention':
                pooled = self.graph_pooling(node_embeddings, batch)
            else:
                raise ValueError(f"Unknown pooling method: {self.pooling_method}")
                
            graph_embedding = self.graph_projection(pooled)
        else:
            # 如果没有batch信息，使用简单池化
            if node_embeddings.size(0) > 0:
                pooled = torch.mean(node_embeddings, dim=0, keepdim=True)
                graph_embedding = self.graph_projection(pooled)
            else:
                # 如果没有节点，返回零向量
                graph_embedding = torch.zeros(1, node_embeddings.size(1) if node_embeddings.size(0) > 0 else self.conv_layers[-1].out_channels, 
                                            device=node_embeddings.device)
        
        return node_embeddings, graph_embedding


class MultiHeadGATLayer(nn.Module):
    """
    多头图注意力层，支持边类型和边权重
    """
    def __init__(self, in_channels, out_channels, num_heads=8, num_edge_types=13,
                 dropout=0.1, edge_dim=None, concat=True, bias=True):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.num_heads = num_heads
        self.num_edge_types = num_edge_types
        self.dropout = dropout
        self.concat = concat

        # 如果concat=True，每个头的维度是 out_channels // num_heads，最终输出是 out_channels
        # 如果concat=False，每个头的维度是 out_channels，最终输出也是 out_channels
        if concat:
            self.head_dim = out_channels // num_heads
            self.final_out_channels = out_channels
        else:
            self.head_dim = out_channels
            self.final_out_channels = out_channels

        # 边类型嵌入
        self.edge_type_embedding = nn.Embedding(num_edge_types, self.head_dim)

        # 多头注意力的线性变换
        self.W_q = nn.Linear(in_channels, self.head_dim * num_heads, bias=False)
        self.W_k = nn.Linear(in_channels, self.head_dim * num_heads, bias=False)
        self.W_v = nn.Linear(in_channels, self.head_dim * num_heads, bias=False)

        # 边特征的线性变换（如果使用边特征）
        if edge_dim is not None:
            self.W_e = nn.Linear(edge_dim, self.head_dim * num_heads, bias=False)
        else:
            self.W_e = None

        # 注意力权重计算
        self.attention = nn.Parameter(torch.Tensor(num_heads, 2 * self.head_dim))

        # 输出投影
        if concat:
            self.out_proj = nn.Linear(self.head_dim * num_heads, self.final_out_channels)
        else:
            self.out_proj = nn.Identity()

        # 偏置
        if bias:
            self.bias = nn.Parameter(torch.Tensor(self.final_out_channels))
        else:
            self.register_parameter('bias', None)

        # 层归一化和dropout
        self.layer_norm = nn.LayerNorm(self.final_out_channels)
        self.dropout_layer = nn.Dropout(dropout)

        self.reset_parameters()

    def reset_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.W_q.weight)
        nn.init.xavier_uniform_(self.W_k.weight)
        nn.init.xavier_uniform_(self.W_v.weight)
        if self.W_e is not None:
            nn.init.xavier_uniform_(self.W_e.weight)
        nn.init.xavier_uniform_(self.attention)
        if self.bias is not None:
            nn.init.zeros_(self.bias)
        nn.init.xavier_uniform_(self.edge_type_embedding.weight)

    def forward(self, x, edge_index, edge_type=None, edge_attr=None, edge_weight=None):
        """
        前向传播

        Args:
            x: 节点特征 [num_nodes, in_channels]
            edge_index: 边索引 [2, num_edges]
            edge_type: 边类型 [num_edges]
            edge_attr: 边特征 [num_edges, edge_dim]
            edge_weight: 边权重 [num_edges]

        Returns:
            out: 输出节点特征 [num_nodes, out_channels]
        """
        num_nodes = x.size(0)

        # 线性变换
        Q = self.W_q(x).view(num_nodes, self.num_heads, self.head_dim)  # [num_nodes, num_heads, head_dim]
        K = self.W_k(x).view(num_nodes, self.num_heads, self.head_dim)
        V = self.W_v(x).view(num_nodes, self.num_heads, self.head_dim)

        # 边的源节点和目标节点
        row, col = edge_index

        # 计算注意力分数
        # Q[row]: [num_edges, num_heads, head_dim]
        # K[col]: [num_edges, num_heads, head_dim]
        q_i = Q[row]  # 源节点的查询
        k_j = K[col]  # 目标节点的键

        # 边类型嵌入
        if edge_type is not None:
            edge_emb = self.edge_type_embedding(edge_type)  # [num_edges, head_dim]
            edge_emb = edge_emb.unsqueeze(1).expand(-1, self.num_heads, -1)  # [num_edges, num_heads, head_dim]
            # 将边类型信息加入键
            k_j = k_j + edge_emb

        # 边特征
        if edge_attr is not None and self.W_e is not None:
            edge_feat = self.W_e(edge_attr).view(-1, self.num_heads, self.head_dim)
            k_j = k_j + edge_feat

        # 拼接查询和键
        qk = torch.cat([q_i, k_j], dim=-1)  # [num_edges, num_heads, 2*head_dim]

        # 计算注意力权重
        alpha = (qk * self.attention.unsqueeze(0)).sum(dim=-1)  # [num_edges, num_heads]
        alpha = F.leaky_relu(alpha, 0.2)

        # 应用边权重
        if edge_weight is not None:
            # edge_weight: [num_edges], alpha: [num_edges, num_heads]
            # 需要将edge_weight扩展到所有注意力头
            if edge_weight.size(0) == alpha.size(0):
                # 维度匹配，直接应用
                alpha = alpha * edge_weight.unsqueeze(-1)
            elif edge_weight.size(0) * self.num_heads == alpha.size(0):
                # 可能是多头注意力导致的维度扩展，重复edge_weight
                expanded_edge_weight = edge_weight.repeat_interleave(self.num_heads)
                alpha = alpha * expanded_edge_weight.unsqueeze(-1)
            else:
                # 维度确实不匹配，使用原始边权重但只应用到对应的边
                # 这种情况下，我们假设alpha的前edge_weight.size(0)个元素对应原始边
                if edge_weight.size(0) <= alpha.size(0):
                    # 创建一个与alpha同样大小的权重张量，默认为1.0
                    full_edge_weight = torch.ones(alpha.size(0), device=alpha.device)
                    # 将原始边权重复制到对应位置
                    full_edge_weight[:edge_weight.size(0)] = edge_weight
                    alpha = alpha * full_edge_weight.unsqueeze(-1)
                else:
                    # 如果edge_weight比alpha还大，只使用前alpha.size(0)个权重
                    alpha = alpha * edge_weight[:alpha.size(0)].unsqueeze(-1)

        # Softmax归一化（按目标节点分组）
        alpha = self.softmax(alpha, col, num_nodes)
        alpha = self.dropout_layer(alpha)

        # 计算输出
        v_j = V[col]  # [num_edges, num_heads, head_dim]
        out = alpha.unsqueeze(-1) * v_j  # [num_edges, num_heads, head_dim]

        # 聚合到目标节点
        out = self.aggregate(out, col, num_nodes)  # [num_nodes, num_heads, head_dim]

        # 重塑和投影
        if self.concat:
            out = out.view(num_nodes, -1)  # [num_nodes, num_heads * head_dim]
        else:
            out = out.mean(dim=1)  # [num_nodes, head_dim]

        out = self.out_proj(out)

        # 添加偏置
        if self.bias is not None:
            out = out + self.bias

        # 残差连接和层归一化
        if out.size(-1) == x.size(-1):
            out = out + x
        out = self.layer_norm(out)

        return out

    def softmax(self, src, index, num_nodes):
        """
        按目标节点分组进行softmax归一化

        Args:
            src: 注意力分数 [num_edges, num_heads]
            index: 目标节点索引 [num_edges]
            num_nodes: 节点总数

        Returns:
            out: 归一化后的注意力权重 [num_edges, num_heads]
        """
        # 计算每个目标节点的最大值（用于数值稳定性）
        out = src - src.max()
        out = out.exp()

        # 按目标节点分组求和
        out_sum = torch.zeros(num_nodes, src.size(1), device=src.device, dtype=src.dtype)
        out_sum.scatter_add_(0, index.unsqueeze(-1).expand(-1, src.size(1)), out)

        # 归一化
        out_sum = out_sum[index]
        out = out / (out_sum + 1e-16)

        return out

    def aggregate(self, src, index, num_nodes):
        """
        将边特征聚合到目标节点

        Args:
            src: 边特征 [num_edges, num_heads, head_dim]
            index: 目标节点索引 [num_edges]
            num_nodes: 节点总数

        Returns:
            out: 聚合后的节点特征 [num_nodes, num_heads, head_dim]
        """
        out = torch.zeros(num_nodes, src.size(1), src.size(2),
                         device=src.device, dtype=src.dtype)

        # 使用scatter_add进行聚合
        index_expanded = index.unsqueeze(-1).unsqueeze(-1).expand(-1, src.size(1), src.size(2))
        out.scatter_add_(0, index_expanded, src)

        return out


class EnhancedGATEncoder(nn.Module):
    """
    增强的图注意力网络编码器
    支持多头注意力、边类型、边权重和位置编码
    """
    def __init__(self, input_size, hidden_size, num_layers=2, num_heads=8,
                 num_edge_types=13, dropout=0.1, use_position_encoding=True,
                 pooling_method='attention'):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.num_edge_types = num_edge_types
        self.dropout = dropout
        self.use_position_encoding = use_position_encoding
        self.pooling_method = pooling_method

        # 输入投影层
        self.input_projection = nn.Linear(input_size, hidden_size)

        # 位置编码
        if use_position_encoding:
            self.position_encoding = PositionalEncoding(hidden_size, max_len=100)

        # GAT层
        self.gat_layers = nn.ModuleList()
        for i in range(num_layers):
            if i == 0:
                in_channels = hidden_size
            else:
                in_channels = hidden_size

            # 最后一层不使用concat
            concat = i < num_layers - 1

            self.gat_layers.append(
                MultiHeadGATLayer(
                    in_channels=in_channels,
                    out_channels=hidden_size,
                    num_heads=num_heads,
                    num_edge_types=num_edge_types,
                    dropout=dropout,
                    concat=concat
                )
            )

        # 图级别的池化
        if pooling_method == 'attention':
            self.graph_attention_pool = GlobalAttention(
                nn.Sequential(
                    nn.Linear(hidden_size, hidden_size // 2),
                    nn.ReLU(),
                    nn.Linear(hidden_size // 2, 1)
                )
            )
        elif pooling_method == 'mean':
            self.graph_attention_pool = None
        elif pooling_method == 'max':
            self.graph_attention_pool = None

        # 图级别特征投影
        self.graph_projection = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size)
        )

        # 层归一化
        self.final_norm = nn.LayerNorm(hidden_size)

    def forward(self, x, edge_index, edge_type=None, edge_attr=None, edge_weight=None, batch=None):
        """
        前向传播

        Args:
            x: 节点特征 [num_nodes, input_size]
            edge_index: 边索引 [2, num_edges]
            edge_type: 边类型 [num_edges]
            edge_attr: 边特征 [num_edges, edge_dim]
            edge_weight: 边权重 [num_edges]
            batch: 批次索引 [num_nodes]

        Returns:
            node_embeddings: 节点嵌入 [num_nodes, hidden_size]
            graph_embedding: 图嵌入 [batch_size, hidden_size]
        """
        # 输入投影
        h = self.input_projection(x)

        # 位置编码
        if self.use_position_encoding:
            h = self.position_encoding(h)

        # 多层GAT
        for i, gat_layer in enumerate(self.gat_layers):
            # 只在第一层使用边权重，后续层不使用
            layer_edge_weight = edge_weight if i == 0 else None
            h_new = gat_layer(h, edge_index, edge_type, edge_attr, layer_edge_weight)

            # 残差连接（如果维度匹配）
            if h.size(-1) == h_new.size(-1):
                h = h + h_new
            else:
                h = h_new

        # 最终层归一化
        node_embeddings = self.final_norm(h)

        # 图级别池化
        if batch is None:
            # 单图情况
            if self.pooling_method == 'attention':
                graph_embedding = self.graph_attention_pool(node_embeddings, torch.zeros(node_embeddings.size(0), dtype=torch.long, device=node_embeddings.device))
            elif self.pooling_method == 'mean':
                graph_embedding = global_mean_pool(node_embeddings, torch.zeros(node_embeddings.size(0), dtype=torch.long, device=node_embeddings.device))
            elif self.pooling_method == 'max':
                graph_embedding = global_max_pool(node_embeddings, torch.zeros(node_embeddings.size(0), dtype=torch.long, device=node_embeddings.device))
        else:
            # 批次情况
            if self.pooling_method == 'attention':
                graph_embedding = self.graph_attention_pool(node_embeddings, batch)
            elif self.pooling_method == 'mean':
                graph_embedding = global_mean_pool(node_embeddings, batch)
            elif self.pooling_method == 'max':
                graph_embedding = global_max_pool(node_embeddings, batch)

        # 图特征投影
        graph_embedding = self.graph_projection(graph_embedding)

        return node_embeddings, graph_embedding


class PositionalEncoding(nn.Module):
    """
    位置编码模块，为图中的节点添加位置信息
    """
    def __init__(self, d_model, max_len=100):
        super().__init__()
        self.d_model = d_model

        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)

        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        self.register_buffer('pe', pe)

    def forward(self, x):
        """
        Args:
            x: 节点特征 [num_nodes, d_model]

        Returns:
            x: 添加位置编码后的特征 [num_nodes, d_model]
        """
        seq_len = x.size(0)
        if seq_len <= self.pe.size(0):
            return x + self.pe[:seq_len]
        else:
            # 如果序列长度超过预设最大长度，使用循环位置编码
            pe_extended = self.pe.repeat((seq_len // self.pe.size(0)) + 1, 1)
            return x + pe_extended[:seq_len]