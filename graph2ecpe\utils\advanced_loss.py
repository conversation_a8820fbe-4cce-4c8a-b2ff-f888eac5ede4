#!/usr/bin/env python3
"""
改进的损失函数模块：标签平滑、Focal Loss、任务特定损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple
import math


class LabelSmoothingCrossEntropy(nn.Module):
    """
    标签平滑交叉熵损失
    """
    
    def __init__(self, smoothing: float = 0.1, ignore_index: int = 0):
        super().__init__()
        self.smoothing = smoothing
        self.ignore_index = ignore_index
        self.confidence = 1.0 - smoothing
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: [batch_size * seq_len, vocab_size]
            targets: [batch_size * seq_len]
        """
        vocab_size = logits.size(-1)
        
        # 创建平滑标签
        smooth_value = self.smoothing / (vocab_size - 1)
        one_hot = torch.zeros_like(logits).fill_(smooth_value)
        one_hot.scatter_(-1, targets.unsqueeze(-1), self.confidence)
        
        # 忽略padding token
        mask = (targets != self.ignore_index)
        one_hot = one_hot * mask.unsqueeze(-1).float()
        
        # 计算KL散度损失
        log_probs = F.log_softmax(logits, dim=-1)
        loss = -(one_hot * log_probs).sum(dim=-1)
        
        # 应用掩码并计算平均损失
        loss = loss * mask.float()
        return loss.sum() / mask.float().sum().clamp(min=1.0)


class FocalLoss(nn.Module):
    """
    Focal Loss：处理类别不平衡问题
    """
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, ignore_index: int = 0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: [batch_size * seq_len, vocab_size]
            targets: [batch_size * seq_len]
        """
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(logits, targets, reduction='none', ignore_index=self.ignore_index)
        
        # 计算概率
        pt = torch.exp(-ce_loss)
        
        # 计算Focal Loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        # 应用掩码
        mask = (targets != self.ignore_index).float()
        focal_loss = focal_loss * mask
        
        return focal_loss.sum() / mask.sum().clamp(min=1.0)


class ECPETaskSpecificLoss(nn.Module):
    """
    ECPE任务特定损失：针对情感-原因对提取的特殊设计
    """
    
    def __init__(self, token2idx: Dict, alpha_structure: float = 0.2, 
                 alpha_diversity: float = 0.05, alpha_consistency: float = 0.1):
        super().__init__()
        self.token2idx = token2idx
        self.alpha_structure = alpha_structure
        self.alpha_diversity = alpha_diversity
        self.alpha_consistency = alpha_consistency
        
        # 重要token的ID
        self.sos_id = token2idx.get('<sos>', 1)
        self.eos_id = token2idx.get('<eos>', 2)
        self.sep_id = token2idx.get('<sep>', 3)
        self.pad_id = token2idx.get('<pad>', 0)
        
        # 情感token的ID
        self.emotion_ids = []
        emotions = ['surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear']
        for emotion in emotions:
            if emotion in token2idx:
                self.emotion_ids.append(token2idx[emotion])
        
        # 说话者token的ID
        self.speaker_ids = []
        speakers = ['Chandler', 'Joey', 'Ross', 'Rachel', 'Monica', 'Phoebe']
        for speaker in speakers:
            if speaker in token2idx:
                self.speaker_ids.append(token2idx[speaker])
        
        # 话语ID的范围（假设utt_000到utt_099）
        self.utterance_ids = []
        for i in range(100):
            utt_token = f'utt_{i:03d}'
            if utt_token in token2idx:
                self.utterance_ids.append(token2idx[utt_token])
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: [batch_size, seq_len, vocab_size]
            targets: [batch_size, seq_len]
        """
        batch_size, seq_len, vocab_size = logits.shape
        
        # 1. 基础交叉熵损失
        logits_flat = logits.view(-1, vocab_size)
        targets_flat = targets.view(-1)
        
        mask = (targets_flat != self.pad_id)
        if mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        base_loss = F.cross_entropy(logits_flat[mask], targets_flat[mask])
        
        # 2. 结构损失：重要token的权重增强
        structure_loss = self._compute_structure_loss(logits_flat, targets_flat, mask)
        
        # 3. 多样性损失：鼓励生成多样化的token
        diversity_loss = self._compute_diversity_loss(logits_flat, mask)
        
        # 4. 一致性损失：鼓励合理的情感-原因对
        consistency_loss = self._compute_consistency_loss(logits, targets)
        
        # 组合损失
        total_loss = (base_loss + 
                     self.alpha_structure * structure_loss +
                     self.alpha_diversity * diversity_loss +
                     self.alpha_consistency * consistency_loss)
        
        return total_loss
    
    def _compute_structure_loss(self, logits_flat: torch.Tensor, 
                               targets_flat: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """计算结构损失：对重要token加权"""
        structure_loss = 0.0
        
        # 对分隔符和结束符加权
        important_tokens = [self.sep_id, self.eos_id]
        
        for token_id in important_tokens:
            token_mask = (targets_flat == token_id) & mask
            if token_mask.any():
                token_logits = logits_flat[token_mask]
                token_targets = targets_flat[token_mask]
                token_loss = F.cross_entropy(token_logits, token_targets)
                structure_loss += token_loss * 0.5  # 权重
        
        return structure_loss
    
    def _compute_diversity_loss(self, logits_flat: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """计算多样性损失：鼓励token分布的多样性"""
        if mask.sum() == 0:
            return torch.tensor(0.0, device=logits_flat.device)
        
        # 计算预测分布的熵
        probs = F.softmax(logits_flat[mask], dim=-1)
        entropy = -(probs * torch.log(probs + 1e-8)).sum(dim=-1).mean()
        
        # 多样性损失：熵越大越好（负熵作为损失）
        diversity_loss = -entropy
        
        return diversity_loss
    
    def _compute_consistency_loss(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """计算一致性损失：鼓励合理的情感-原因对结构"""
        batch_size, seq_len, vocab_size = logits.shape
        consistency_loss = 0.0
        
        # 检查每个序列的结构一致性
        for b in range(batch_size):
            target_seq = targets[b]
            logit_seq = logits[b]
            
            # 寻找三元组模式：speaker emotion cause
            for i in range(seq_len - 2):
                if (target_seq[i] != self.pad_id and 
                    target_seq[i+1] != self.pad_id and 
                    target_seq[i+2] != self.pad_id):
                    
                    # 检查是否为合理的三元组
                    speaker_id = target_seq[i].item()
                    emotion_id = target_seq[i+1].item()
                    cause_id = target_seq[i+2].item()
                    
                    # 如果是合理的三元组，增强对应位置的损失
                    if (self._is_speaker_or_utterance(speaker_id) and
                        emotion_id in self.emotion_ids and
                        self._is_speaker_or_utterance(cause_id)):
                        
                        # 对这个三元组的预测加权
                        triplet_loss = 0.0
                        for j in range(3):
                            pos_logits = logit_seq[i+j]
                            pos_target = target_seq[i+j]
                            triplet_loss += F.cross_entropy(pos_logits.unsqueeze(0), 
                                                          pos_target.unsqueeze(0))
                        
                        consistency_loss += triplet_loss * 0.1
        
        return consistency_loss / max(1, batch_size)
    
    def _is_speaker_or_utterance(self, token_id: int) -> bool:
        """检查是否为说话者或话语ID"""
        return (token_id in self.speaker_ids or 
                token_id in self.utterance_ids)


class AdaptiveLossScheduler:
    """
    自适应损失调度器：根据训练进度调整损失权重
    """
    
    def __init__(self, total_epochs: int):
        self.total_epochs = total_epochs
        self.current_epoch = 0
    
    def step(self, epoch: int):
        """更新当前epoch"""
        self.current_epoch = epoch
    
    def get_loss_weights(self) -> Dict[str, float]:
        """获取当前epoch的损失权重"""
        progress = self.current_epoch / self.total_epochs
        
        # 早期训练：注重基础损失
        # 后期训练：增加任务特定损失的权重
        if progress < 0.3:
            return {
                'base_weight': 1.0,
                'structure_weight': 0.1,
                'diversity_weight': 0.02,
                'consistency_weight': 0.05
            }
        elif progress < 0.7:
            return {
                'base_weight': 1.0,
                'structure_weight': 0.2,
                'diversity_weight': 0.05,
                'consistency_weight': 0.1
            }
        else:
            return {
                'base_weight': 1.0,
                'structure_weight': 0.3,
                'diversity_weight': 0.08,
                'consistency_weight': 0.15
            }


def create_advanced_loss_function(loss_type: str, token2idx: Dict, **kwargs):
    """
    创建高级损失函数
    
    Args:
        loss_type: 损失函数类型 ('label_smoothing', 'focal', 'ecpe_specific', 'combined')
        token2idx: token到索引的映射
        **kwargs: 其他参数
    
    Returns:
        损失函数实例
    """
    if loss_type == 'label_smoothing':
        return LabelSmoothingCrossEntropy(
            smoothing=kwargs.get('smoothing', 0.1),
            ignore_index=kwargs.get('ignore_index', 0)
        )
    
    elif loss_type == 'focal':
        return FocalLoss(
            alpha=kwargs.get('alpha', 0.25),
            gamma=kwargs.get('gamma', 2.0),
            ignore_index=kwargs.get('ignore_index', 0)
        )
    
    elif loss_type == 'ecpe_specific':
        return ECPETaskSpecificLoss(
            token2idx=token2idx,
            alpha_structure=kwargs.get('alpha_structure', 0.2),
            alpha_diversity=kwargs.get('alpha_diversity', 0.05),
            alpha_consistency=kwargs.get('alpha_consistency', 0.1)
        )
    
    elif loss_type == 'combined':
        # 组合多种损失函数
        class CombinedLoss(nn.Module):
            def __init__(self):
                super().__init__()
                self.label_smoothing = LabelSmoothingCrossEntropy(
                    smoothing=kwargs.get('smoothing', 0.1),
                    ignore_index=kwargs.get('ignore_index', 0)
                )
                self.ecpe_specific = ECPETaskSpecificLoss(
                    token2idx=token2idx,
                    alpha_structure=kwargs.get('alpha_structure', 0.1),
                    alpha_diversity=kwargs.get('alpha_diversity', 0.03),
                    alpha_consistency=kwargs.get('alpha_consistency', 0.05)
                )
                self.weight_smoothing = kwargs.get('weight_smoothing', 0.7)
                self.weight_ecpe = kwargs.get('weight_ecpe', 0.3)
            
            def forward(self, logits, targets):
                batch_size, seq_len, vocab_size = logits.shape
                logits_flat = logits.view(-1, vocab_size)
                targets_flat = targets.view(-1)
                
                loss1 = self.label_smoothing(logits_flat, targets_flat)
                loss2 = self.ecpe_specific(logits, targets)
                
                return self.weight_smoothing * loss1 + self.weight_ecpe * loss2
        
        return CombinedLoss()
    
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")
