"""
Graph Decoder of Graph2Seq Architecture for Emotion-Cause Pair Extraction

Date:
    - Current Date
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils.rnn import pack_padded_sequence, pad_packed_sequence
import random
import numpy as np


class AttentionLayer(nn.Module):
    """Attention layer for the decoder to attend to node representations"""
    def __init__(self, hidden_size):
        super().__init__()
        self.hidden_size = hidden_size
        self.query_projection = nn.Linear(hidden_size, hidden_size)
        self.key_projection = nn.Linear(hidden_size, hidden_size)
        self.value_projection = nn.Linear(hidden_size, hidden_size)
        self.energy = nn.Linear(hidden_size, 1)
        
        # 情感-原因节点的额外注意力权重（针对情感-原因对抽取任务）
        self.ec_node_weight = 2.0
        
    def forward(self, hidden, encoder_outputs, emotion_cause_mask=None, node_mask=None):
        """
        改进的注意力机制实现

        Args:
            hidden: 解码器隐藏状态 [batch_size, hidden_size]
            encoder_outputs: 编码器输出 [num_nodes, hidden_size]
            emotion_cause_mask: 情感-原因节点掩码 [batch_size, num_nodes]
            node_mask: 节点掩码 [batch_size, num_nodes]

        Returns:
            context: 上下文向量 [batch_size, hidden_size]
            attention_weights: 注意力权重 [batch_size, num_nodes]
        """
        batch_size = hidden.size(0)
        num_nodes = encoder_outputs.size(0)

        # 投影解码器隐藏状态作为查询
        query = self.query_projection(hidden).unsqueeze(1)  # [batch_size, 1, hidden_size]

        # 投影编码器输出作为键和值
        key = self.key_projection(encoder_outputs)  # [num_nodes, hidden_size]
        value = self.value_projection(encoder_outputs)  # [num_nodes, hidden_size]

        # 扩展键和值到批次维度
        key = key.unsqueeze(0).expand(batch_size, -1, -1)  # [batch_size, num_nodes, hidden_size]
        value = value.unsqueeze(0).expand(batch_size, -1, -1)  # [batch_size, num_nodes, hidden_size]
        
        # 计算注意力分数 (general注意力)
        scores = torch.bmm(query, key.transpose(1, 2))  # [batch_size, 1, num_nodes]
        scores = scores.squeeze(1)  # [batch_size, num_nodes]

        # 应用情感-原因节点掩码来增强这些节点的重要性
        if emotion_cause_mask is not None:
            # 确保掩码维度匹配
            if emotion_cause_mask.size(-1) == scores.size(-1):
                if emotion_cause_mask.dim() == 1:
                    emotion_cause_mask = emotion_cause_mask.unsqueeze(0).expand(batch_size, -1)
                # 为情感-原因节点添加额外的注意力权重
                scores = scores + emotion_cause_mask * self.ec_node_weight

        # 应用节点掩码
        if node_mask is not None:
            # 确保掩码维度匹配
            if node_mask.size(-1) == scores.size(-1):
                if node_mask.dim() == 1:
                    node_mask = node_mask.unsqueeze(0).expand(batch_size, -1)
                scores = scores.masked_fill(node_mask == 0, -1e9)

        # 对分数进行softmax以获得注意力权重
        attention_weights = F.softmax(scores, dim=1)  # [batch_size, num_nodes]
        
        # 使用注意力权重计算上下文向量
        context = torch.bmm(attention_weights.unsqueeze(1), value).squeeze(1)  # [batch_size, hidden_size]
        
        return context, attention_weights


class DecoderRNN(nn.Module):
    """Decoder for generating emotion-cause pairs"""
    def __init__(self, hidden_size, output_size, num_layers=1, dropout=0.1, max_length=50):
        super().__init__()
        self.hidden_size = hidden_size
        self.output_size = output_size
        self.num_layers = num_layers
        self.max_length = max_length
        
        # 主要组件
        self.embedding = nn.Embedding(output_size, hidden_size, padding_idx=0)
        self.attention = AttentionLayer(hidden_size)
        self.gru = nn.GRU(hidden_size * 2, hidden_size, num_layers=num_layers, 
                         dropout=(dropout if num_layers > 1 else 0), batch_first=True)
        self.out = nn.Linear(hidden_size * 2, output_size)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, input_step, hidden, encoder_outputs, node_mask=None, emotion_cause_mask=None):
        """
        单步解码
        
        Args:
            input_step: 输入token [batch_size, 1]
            hidden: 隐藏状态 [num_layers, batch_size, hidden_size]
            encoder_outputs: 编码器输出 [num_nodes, hidden_size]
            node_mask: 节点掩码 [batch_size, num_nodes]
            emotion_cause_mask: 情感-原因节点掩码 [batch_size, num_nodes]
            
        Returns:
            output: 输出分布 [batch_size, output_size]
            hidden: 更新的隐藏状态 [num_layers, batch_size, hidden_size]
            attn_weights: 注意力权重 [batch_size, num_nodes]
        """
        batch_size = input_step.size(0)
        
        # Embed input
        embedded = self.embedding(input_step)  # [batch_size, 1, hidden_size]
        embedded = self.dropout(embedded).squeeze(1)  # [batch_size, hidden_size]
        
        # Calculate attention
        context, attn_weights = self.attention(
            hidden[-1], encoder_outputs, emotion_cause_mask, node_mask
        )  # context: [batch_size, hidden_size], attn_weights: [batch_size, num_nodes]
        
        # Combine embedded input and attention context
        rnn_input = torch.cat((embedded, context), dim=1)  # [batch_size, hidden_size*2]
        rnn_input = rnn_input.unsqueeze(1)  # [batch_size, 1, hidden_size*2]
        
        # Pass through GRU
        output, hidden = self.gru(rnn_input, hidden)  # output: [batch_size, 1, hidden_size]
        output = output.squeeze(1)  # [batch_size, hidden_size]
        
        # Calculate output distribution
        output = self.out(torch.cat((output, context), 1))  # [batch_size, output_size]
        # 不在这里应用log_softmax，让调用者决定
        
        return output, hidden, attn_weights
    
    def init_hidden(self, batch_size, device):
        """Initialize hidden state"""
        return torch.zeros(self.num_layers, batch_size, self.hidden_size, device=device)


class Graph2SeqDecoder(nn.Module):
    """Graph2Seq Decoder for Emotion-Cause Pair Extraction"""
    def __init__(self, hidden_size, vocab_size, num_layers=1, dropout=0.1, max_length=50):
        super().__init__()
        self.hidden_size = hidden_size
        self.vocab_size = vocab_size
        self.max_length = max_length
        
        # Decoder
        self.decoder = DecoderRNN(hidden_size, vocab_size, num_layers, dropout, max_length)
        
        # Bridge between encoder and decoder
        self.bridge = nn.Linear(hidden_size, hidden_size * num_layers)
        
        # Special token IDs
        self.SOS_token = 1  # Start of sequence token
        self.EOS_token = 2  # End of sequence token
        self.PAD_token = 0  # Padding token
        
    def forward(self, node_features, graph_emb, target_seq=None, node_batch=None, teacher_forcing_ratio=0.5, emotion_cause_nodes=None):
        """
        解码生成情感-原因对序列
        
        Args:
            node_features: 节点特征 [num_nodes, hidden_size]
            graph_emb: 图嵌入 [batch_size, hidden_size]
            target_seq: 目标序列 [batch_size, max_length]
            node_batch: 节点批次索引 [num_nodes]
            teacher_forcing_ratio: teacher forcing比率
            emotion_cause_nodes: 情感-原因节点列表 [[node_idx, ...], ...]
            
        Returns:
            decoder_outputs: 解码器输出 [batch_size, max_length, vocab_size]
            attention_weights: 注意力权重 [batch_size, max_length, num_nodes]
        """
        device = node_features.device
        
        # 安全处理空图
        if node_features.size(0) == 0:
            node_features = torch.zeros(1, self.hidden_size, device=device)
            if node_batch is None:
                node_batch = torch.zeros(1, dtype=torch.long, device=device)
        
        # 确定批次大小
        batch_size = 1
        if target_seq is not None:
            batch_size = target_seq.size(0)
        elif graph_emb.size(0) > 1:
            batch_size = graph_emb.size(0)
        elif node_batch is not None and node_batch.size(0) > 0:
            batch_size = node_batch.max().item() + 1
        
        # 确保graph_emb形状正确
        if graph_emb.dim() == 1:
            graph_emb = graph_emb.unsqueeze(0)
        
        if graph_emb.size(0) != batch_size:
            # 重复或截断graph_emb以匹配batch_size
            if graph_emb.size(0) == 1:
                graph_emb = graph_emb.expand(batch_size, -1)
            else:
                new_graph_emb = torch.zeros(batch_size, self.hidden_size, device=device)
                valid_size = min(graph_emb.size(0), batch_size)
                new_graph_emb[:valid_size] = graph_emb[:valid_size]
                graph_emb = new_graph_emb
                
        # 创建节点掩码和情感-原因节点掩码
        node_mask = torch.ones(batch_size, node_features.size(0), device=device)
        emotion_cause_mask = torch.zeros(batch_size, node_features.size(0), device=device)
        
        # 如果提供了节点批次索引，创建相应的掩码
        if node_batch is not None and node_batch.size(0) > 0:
            # 根据batch分配创建节点掩码
            node_mask = torch.zeros(batch_size, node_features.size(0), device=device)
            for b in range(batch_size):
                batch_nodes = (node_batch == b).nonzero(as_tuple=True)[0]
                if batch_nodes.size(0) > 0:
                    node_mask[b, batch_nodes] = 1
                else:
                    # 如果该batch没有节点，允许关注所有节点
                    node_mask[b, :] = 1
        
        # 如果提供了情感-原因节点，创建相应的掩码
        if emotion_cause_nodes is not None:
            for b, nodes in enumerate(emotion_cause_nodes):
                if b < batch_size and nodes:
                    for node in nodes:
                        if 0 <= node < node_features.size(0):
                            emotion_cause_mask[b, node] = 1
        
        # 初始化解码器隐藏状态
        bridge_output = self.bridge(graph_emb)  # [batch_size, hidden_size * num_layers]
        decoder_hidden = bridge_output.view(self.decoder.num_layers, batch_size, self.hidden_size)
        
        # 初始输入为SOS token
        decoder_input = torch.full((batch_size, 1), self.SOS_token, dtype=torch.long, device=device)
        
        # 确定序列长度
        max_target_length = self.max_length
        if target_seq is not None:
            max_target_length = target_seq.size(1)
            
        # 初始化输出和注意力权重
        decoder_outputs = torch.zeros(batch_size, max_target_length, self.vocab_size, device=device)
        attention_weights = torch.zeros(batch_size, max_target_length, node_features.size(0), device=device)
        
        # 随机决定是否使用teacher forcing
        use_teacher_forcing = random.random() < teacher_forcing_ratio
        
        # 逐步解码
        for t in range(max_target_length):
            # 单步解码
            decoder_output, decoder_hidden, attn_weights = self.decoder(
                decoder_input, decoder_hidden, node_features, node_mask, emotion_cause_mask
            )
            
            # 存储输出和注意力权重
            decoder_outputs[:, t, :] = decoder_output
            attention_weights[:, t, :] = attn_weights
            
            # 准备下一步输入
            if use_teacher_forcing and target_seq is not None and t < max_target_length - 1:
                # Teacher forcing: 使用真实目标作为下一步输入
                decoder_input = target_seq[:, t+1].unsqueeze(1)
            else:
                # 非teacher forcing: 使用当前预测作为下一步输入
                topv, topi = decoder_output.topk(1)
                decoder_input = topi.detach()  # 从计算图中分离
        
        return decoder_outputs, attention_weights


if __name__ == '__main__':
    # Test code
    hidden_size = 128
    vocab_size = 100
    batch_size = 2
    max_seq_len = 10
    num_nodes = 15
    
    # Create sample data
    node_features = torch.randn(num_nodes, hidden_size)
    graph_emb = torch.randn(1, hidden_size)
    target_seq = torch.randint(0, vocab_size, (batch_size, max_seq_len))
    node_batch = torch.cat([torch.zeros(8, dtype=torch.long), torch.ones(7, dtype=torch.long)])
    
    # Create decoder
    decoder = Graph2SeqDecoder(hidden_size, vocab_size)
    
    # Test forward pass
    outputs, attn = decoder(node_features, graph_emb, target_seq, node_batch)
    print(f"Output shape: {outputs.shape}")
    print(f"Attention shape: {attn.shape}") 