import torch


def _create_default_sequence(token2idx, max_length):
    """Create a default target sequence when processing fails"""
    pad_id = token2idx['<pad>']
    sos_id = token2idx['<sos>']
    eos_id = token2idx['<eos>']
    none_id = token2idx['_NONE']

    # 创建基本序列: <sos> _NONE _NONE _NONE <eos> <pad>...
    seq = [sos_id, none_id, none_id, none_id, eos_id] + [pad_id] * (max_length - 5)
    return torch.tensor(seq)


def target_seq_from_links(links, token2idx, emotions, max_length=20, emotion_categories=None):
    """
    Convert emotion-cause links to target sequences for the decoder using utterance ID format

    Format: <sos> utt_id1 emotion1 utt_id2 <sep> utt_id3 emotion3 utt_id4 <sep> ... <eos>

    Args:
        links: List of emotion-cause links [(source_id, target_id), ...]
        token2idx: Dictionary mapping tokens to indices
        emotions: List of emotion IDs for utterances
        max_length: Maximum sequence length
        emotion_categories: List of emotion category names (optional, will be inferred if not provided)

    Returns:
        target_seq: Tensor of token indices [max_length]
    """
    # Get special token IDs
    sos_id = token2idx['<sos>']
    eos_id = token2idx['<eos>']
    sep_id = token2idx['<sep>']
    pad_id = token2idx['<pad>']
    none_id = token2idx['_NONE']
    
    # 获取词汇表大小，用于边界检查
    vocab_size = len(token2idx)

    # 推断情感类别（如果没有提供）
    if emotion_categories is None:
        # 从词汇表中推断情感类别
        emotion_categories = []
        for token in token2idx.keys():
            if token not in ['<pad>', '<sos>', '<eos>', '<sep>', '_NONE'] and not token.startswith('utt_'):
                # 检查是否是说话者名称（通常首字母大写）
                if not (token[0].isupper() and len(token) > 1):
                    emotion_categories.append(token)

        # 如果推断失败，使用默认情感类别
        if not emotion_categories:
            emotion_categories = ['_NONE', 'joy', 'anger', 'surprise', 'sadness', 'fear', 'disgust', 'neutral']

    # Build sequence
    seq = [sos_id]

    for src, trg in links:
        # 确保源和目标索引在有效范围内
        if src < 0 or trg < 0 or src >= len(emotions) or trg >= len(emotions):
            continue

        # Add source utterance ID (emotion utterance)
        src_utt_id = f"utt_{src:03d}"  # 格式化为utt_001, utt_002等
        src_utt_token_id = token2idx.get(src_utt_id, none_id)
        seq.append(src_utt_token_id)
        
        # Add source emotion
        try:
            emotion_idx = emotions[src]
            if 0 <= emotion_idx < len(emotion_categories):
                src_emotion = emotion_categories[emotion_idx]
                src_emotion_id = token2idx.get(src_emotion, none_id)
                if src_emotion_id >= vocab_size:  # 安全检查
                    src_emotion_id = none_id
                seq.append(src_emotion_id)
            else:
                # 情感索引超出范围，使用_NONE
                seq.append(none_id)
        except (IndexError, KeyError):
            seq.append(none_id)

        # Add target utterance ID (cause utterance)
        trg_utt_id = f"utt_{trg:03d}"  # 格式化为utt_001, utt_002等
        trg_utt_token_id = token2idx.get(trg_utt_id, none_id)
        seq.append(trg_utt_token_id)
        
        # Add separator for next pair
        seq.append(sep_id)
        
    # Add EOS token
    seq.append(eos_id)
    
    # Pad sequence to max_length
    if len(seq) < max_length:
        seq += [pad_id] * (max_length - len(seq))
    else:
        seq = seq[:max_length]
        if seq[-1] != eos_id:
            seq[-1] = eos_id  # Ensure last token is EOS
    
    return torch.tensor(seq)


def prepare_target_sequences(batch, token2idx, max_length=20):
    """
    Prepare target sequences for a batch using utterance ID format

    Args:
        batch: Batch data containing dialogue information
        token2idx: Token to index mapping
        max_length: Maximum sequence length

    Returns:
        target_seqs: Tensor of target sequences [batch_size, max_length]
    """
    try:
        # 从词汇表中推断情感类别
        emotion_categories = []
        for token in token2idx.keys():
            if token not in ['<pad>', '<sos>', '<eos>', '<sep>', '_NONE'] and not token.startswith('utt_'):
                # 检查是否是说话者名称（通常首字母大写）
                if not (token[0].isupper() and len(token) > 1):
                    emotion_categories.append(token)

        # 如果推断失败，使用默认情感类别
        if not emotion_categories:
            emotion_categories = ['_NONE', 'joy', 'anger', 'surprise', 'sadness', 'fear', 'disgust', 'neutral']
        # 根据batch长度确定使用哪种格式
        if len(batch) == 9:
            # 兼容旧格式 - 使用改进的边类型识别
            _, _, _, _, utt_speakers, utt_emotions, edge_index_batch, edge_type_batch, _ = batch

            batch_size = len(edge_index_batch)
            target_seqs = []

            # 使用边类型信息准确识别情感-原因对边
            for b in range(batch_size):
                try:
                    links = []
                    if b < len(edge_index_batch) and b < len(edge_type_batch):
                        # 获取当前对话的边索引和边类型
                        edges = edge_index_batch[b]
                        edge_types = edge_type_batch[b]

                        # 只选择情感-原因对边（边类型为0）
                        for i, edge_type in enumerate(edge_types):
                            if i < len(edges) and edge_type == 0:  # EDGE_TYPE_EMOTION_CAUSE = 0
                                links.append(edges[i])

                    # 确保target_seqs数组长度与batch_size一致
                    if b < len(utt_speakers) and b < len(utt_emotions):
                        # 将链接转换为目标序列
                        target_seq = target_seq_from_links(
                            links,
                            token2idx,
                            utt_emotions[b].tolist(),
                            max_length,
                            emotion_categories=emotion_categories
                        )
                        target_seqs.append(target_seq)
                    else:
                        # 如果索引超出范围，创建一个全填充的序列
                        target_seq = _create_default_sequence(token2idx, max_length)
                        target_seqs.append(target_seq)
                except Exception as e:
                    print(f"处理对话 {b} 的目标序列时出错: {e}")
                    # 创建默认序列
                    target_seq = _create_default_sequence(token2idx, max_length)
                    target_seqs.append(target_seq)
        else:
            # 新格式包含情感-原因对边的索引
            _, _, _, _, utt_speakers, utt_emotions, edge_index_batch, _, _, emotion_cause_edge_indices_batch = batch

            batch_size = len(edge_index_batch)
            target_seqs = []

            # 直接使用提供的情感-原因对边索引
            for b in range(batch_size):
                try:
                    links = []
                    if b < len(edge_index_batch) and b < len(emotion_cause_edge_indices_batch):
                        # 获取当前对话的边索引和情感-原因对边索引
                        edges = edge_index_batch[b]
                        ec_indices = emotion_cause_edge_indices_batch[b]

                        # 安全地获取情感-原因对边
                        for idx in ec_indices:
                            if 0 <= idx < len(edges):
                                links.append(edges[idx])

                    # 确保target_seqs数组长度与batch_size一致
                    if b < len(utt_speakers) and b < len(utt_emotions):
                        # 将链接转换为目标序列
                        target_seq = target_seq_from_links(
                            links,
                            token2idx,
                            utt_emotions[b].tolist(),
                            max_length,
                            emotion_categories=emotion_categories
                        )
                        target_seqs.append(target_seq)
                    else:
                        # 如果索引超出范围，创建一个全填充的序列
                        target_seq = _create_default_sequence(token2idx, max_length)
                        target_seqs.append(target_seq)
                except Exception as e:
                    print(f"处理对话 {b} 的目标序列时出错: {e}")
                    # 创建默认序列
                    target_seq = _create_default_sequence(token2idx, max_length)
                    target_seqs.append(target_seq)
        
        # 确保所有序列长度一致
        for i, seq in enumerate(target_seqs):
            if seq.size(0) != max_length:
                print(f"警告: 序列 {i} 长度 {seq.size(0)} 与最大长度 {max_length} 不一致，将调整")
                # 填充或截断
                pad_id = token2idx['<pad>']
                if seq.size(0) < max_length:
                    padded = torch.cat([seq, torch.tensor([pad_id] * (max_length - seq.size(0)))])
                    target_seqs[i] = padded
                else:
                    target_seqs[i] = seq[:max_length]
        
        return torch.stack(target_seqs)
    except Exception as e:
        print(f"prepare_target_sequences出错: {e}")
        # 创建一个默认的目标序列批次
        batch_size = 1
        if isinstance(batch, (list, tuple)) and len(batch) > 6:
            try:
                # 尝试从batch获取大小
                if hasattr(batch[0], 'size') and len(batch[0].size()) > 0:
                    batch_size = batch[0].size(0)
                elif isinstance(batch[6], list):
                    batch_size = len(batch[6])
            except:
                pass
        
        # 创建默认序列
        pad_id = token2idx['<pad>']
        sos_id = token2idx['<sos>']
        eos_id = token2idx['<eos>']
        none_id = token2idx['_NONE']
        
        default_seq = torch.tensor([sos_id, none_id, none_id, none_id, eos_id] + [pad_id] * (max_length - 5))
        return default_seq.unsqueeze(0).repeat(batch_size, 1)





def utterances_to_dialogues(utts):
    res = {}
    for k, v in utts.items():
        uttid = k.split("_")[0]
        if uttid not in res:
            res[uttid] = []

        res[uttid].append(v)
    return res


def dialogues_to_utterances(dials):
    res = {}
    for k, v in dials.items():
        for utt in v:
            res[utt.utterance_id] = utt
    return res





def compute_loss(logits, targets, pad_idx=0):
    """
    计算交叉熵损失

    Args:
        logits: 模型输出 [batch_size, seq_len, vocab_size]
        targets: 目标序列 [batch_size, seq_len]
        pad_idx: padding token的索引

    Returns:
        loss: 计算得到的损失值
    """
    import torch
    import torch.nn as nn

    batch_size, seq_len, vocab_size = logits.shape

    # 展平logits和targets
    logits_flat = logits.view(-1, vocab_size)
    targets_flat = targets.view(-1)

    # 创建掩码，忽略padding tokens
    mask = targets_flat != pad_idx
    if mask.sum() == 0:
        return torch.tensor(0.0, device=logits.device, requires_grad=True)

    # 只计算非padding位置的损失
    logits_flat = logits_flat[mask]
    targets_flat = targets_flat[mask]

    # 计算交叉熵损失
    loss = nn.CrossEntropyLoss()(logits_flat, targets_flat)

    return loss


def extract_pairs_from_sequence(tokens):
    """
    从token序列中提取情感-原因对（使用话语ID格式）

    序列格式: ['<sos>', 'utt_001', 'emotion1', 'utt_002', '<sep>', 'utt_003', 'emotion3', 'utt_004', '<sep>', ..., '<eos>']

    Args:
        tokens: token列表

    Returns:
        pairs: 情感-原因对列表 [(emo_utt_id, emotion, cause_utt_id), ...]
    """
    pairs = []

    if not tokens or len(tokens) < 4:
        return pairs

    # 移除特殊tokens并分割
    if '<sos>' in tokens:
        start_idx = tokens.index('<sos>') + 1
    else:
        start_idx = 0

    if '<eos>' in tokens:
        end_idx = tokens.index('<eos>')
        tokens = tokens[start_idx:end_idx]
    else:
        tokens = tokens[start_idx:]

    # 按<sep>分割序列
    segments = []
    current_segment = []

    for token in tokens:
        if token == '<sep>':
            if len(current_segment) >= 3:  # 至少需要speaker, emotion, cause_speaker
                segments.append(current_segment)
            current_segment = []
        else:
            current_segment.append(token)

    # 添加最后一个段落（如果没有以<sep>结尾）
    if len(current_segment) >= 3:
        segments.append(current_segment)

    # 从每个段落提取情感-原因对
    for segment in segments:
        if len(segment) >= 3:
            speaker = segment[0]
            emotion = segment[1]
            cause_speaker = segment[2]
            pairs.append((speaker, emotion, cause_speaker))

    return pairs


def compute_ecpe_metrics(predictions, targets, idx2token):
    """
    计算ECPE任务的评估指标

    Args:
        predictions: 预测序列 [batch_size, seq_len]
        targets: 目标序列 [batch_size, seq_len]
        idx2token: 索引到token的映射字典

    Returns:
        metrics: 包含precision, recall, f1的字典
    """
    import torch

    if predictions.size(0) == 0:
        return {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}

    all_pred_pairs = []
    all_true_pairs = []

    # 处理每个样本
    for i in range(predictions.size(0)):
        # 转换预测序列为tokens
        pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in predictions[i]]
        true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in targets[i]]

        # 提取情感-原因对
        pred_pairs = extract_pairs_from_sequence(pred_tokens)
        true_pairs = extract_pairs_from_sequence(true_tokens)

        all_pred_pairs.extend(pred_pairs)
        all_true_pairs.extend(true_pairs)

    # 转换为集合以便计算交集
    pred_set = set(all_pred_pairs)
    true_set = set(all_true_pairs)

    # 计算指标
    if len(pred_set) == 0 and len(true_set) == 0:
        precision = recall = f1 = 1.0
    elif len(pred_set) == 0:
        precision = recall = f1 = 0.0
    elif len(true_set) == 0:
        precision = recall = f1 = 0.0
    else:
        intersection = pred_set & true_set
        precision = len(intersection) / len(pred_set) if len(pred_set) > 0 else 0.0
        recall = len(intersection) / len(true_set) if len(true_set) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

    return {
        'precision': precision,
        'recall': recall,
        'f1': f1
    }
