import random
import re
from collections import Counter

import numpy as np
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
from transformers import AutoTokenizer

from .dialogues_data_loader import load_dialogues_json_data
from utils import utterances_to_dialogues

# 定义边类型常量 - 扩展的边类型分类
EDGE_TYPE_EMOTION_CAUSE = 0      # 情感-原因对边
EDGE_TYPE_TEMPORAL_FORWARD = 1   # 时序前向边 (i -> i+1, i+2, ...)
EDGE_TYPE_TEMPORAL_BACKWARD = 2  # 时序后向边 (i -> i-1, i-2, ...)
EDGE_TYPE_SAME_SPEAKER = 3       # 同说话者边
EDGE_TYPE_DIFF_SPEAKER = 4       # 不同说话者边

# 新增：情感语义边
EDGE_TYPE_SAME_EMOTION = 5       # 相同情感边
EDGE_TYPE_EMOTION_TRANSITION = 6 # 情感转换边
EDGE_TYPE_EMOTION_CONTRAST = 7   # 情感对比边

# 新增：语义相似性边
EDGE_TYPE_SEMANTIC_SIMILAR = 8   # 语义相似边

# 新增：因果依赖边
EDGE_TYPE_CAUSAL_DEPENDENCY = 9  # 因果依赖边

# 新增：对话结构边
EDGE_TYPE_QUESTION_ANSWER = 10   # 问答对边
EDGE_TYPE_RESPONSE_RELATION = 11 # 回应关系边
EDGE_TYPE_TOPIC_SHIFT = 12       # 话题转换边

# 边类型范围定义
EDGE_TYPE_RANGES = {
    'emotion_cause': (0, 0),
    'temporal_forward': (1, 1),
    'temporal_backward': (2, 2),
    'same_speaker': (3, 3),
    'diff_speaker': (4, 4)
}

class DialogDataset(Dataset):
    def __init__(self, dataset_type=None, tokenizer="roberta", input_length=512, window_past=3, window_future=3,
                 emotion_categories=None, speakers=None, data_path=None, split=None):
        super().__init__()

        self.window_past = window_past
        self.window_future = window_future

        # 处理数据集类型参数（向后兼容）
        if split is not None:
            dataset_type = split

        # 如果tokenizer是字符串，则从预训练模型加载；否则直接使用传入的tokenizer对象
        if isinstance(tokenizer, str):
            pretrained_model = "/userdata2/fengweijie/" + tokenizer
            tokenizer = AutoTokenizer.from_pretrained(pretrained_model)
        # 如果tokenizer已经是tokenizer对象，直接使用
        if dataset_type is None:
            d = {}
        else:
            utts = load_dialogues_json_data(dataset_type=dataset_type, data_path=data_path)
            d = utterances_to_dialogues(utts)
            self.max_n_utt = max([len(v) for k, v in d.items()])

        self.input_length = input_length

        self.CLS = tokenizer.cls_token_id
        self.SEP = tokenizer.sep_token_id
        self.PAD = tokenizer.pad_token_id
        self.tokenizer = tokenizer

        # 使用传入的emotion_categories和speakers，如果没有则使用默认值
        if emotion_categories is None:
            emotion_categories = ["_NONE", "surprise", "joy", "sadness", "neutral", "disgust", "anger", "fear"]
        if speakers is None:
            speakers = ["_NONE", "Chandler", "Joey", "Ross", "Rachel", "Monica", "Phoebe"]

        assert emotion_categories[0] == '_NONE'
        assert speakers[0] == '_NONE'

        self.em2i = {}
        for i, v in enumerate(emotion_categories):
            self.em2i[v] = i

        self.sp2i = {}
        for i, v in enumerate(speakers):
            self.sp2i[v] = i

        # 扩展边类型定义 - 使用13种边类型
        # 0-4: 基础边类型, 5-7: 情感语义边, 8: 语义相似边, 9: 因果依赖边, 10-12: 对话结构边
        self.max_edge_type_id = 13  # 使用13种边类型

        dialogs = []
        for k, utterances in tqdm(d.items(), desc="loading dialogs"):
            dialogs.append(self.prepare_dialog(utterances))

        self.d = dialogs

    def __len__(self):
        return len(self.d)

    def _prepare_dialog(self, utterances):
        texts = []
        emotions = []
        speakers = []
        links = []

        for utt in utterances:
            texts.append(utt.utterance_text)
            if utt.utterance_emotion not in self.em2i:
                emotions.append(0)
            else:
                emotions.append(self.em2i[utt.utterance_emotion])

            if utt.utterance_speaker not in self.sp2i:
                speakers.append(0)
            else:
                speakers.append(self.sp2i[utt.utterance_speaker])

            for link in utt.emotion_cause_links:
                srcid = int(link.source_id) - 1
                trgid = int(link.target_id) - 1
                links.append([srcid, trgid])

        tokens = self.tokenizer(texts, add_special_tokens=False)
       
        return (
            tokens['input_ids'],
            speakers,
            emotions,
            links,
        )

    def _pad(self, l, padval, padsize):
        # 如果内容长度超过padsize，进行截断
        if len(l) > padsize:
            return l[:padsize]
        # 否则进行填充
        return l + [padval] * (padsize - len(l))

    def prepare_dialog(self, utterances, skip_links=False):
        assert len(utterances) <= self.max_n_utt, f"max_n_utt is set to {self.max_n_utt}, but {len(utterances)} occured"
        tokens, utt_speakers, utt_emotions, links = self._prepare_dialog(utterances)

        # 构建对话图结构的基本数据（不转换为tensor）
        dialog_length = len(utt_speakers)
        edge_index_list = []
        edge_type_list = []
        edge_norm_list = []
        
        # 存储情感-原因对边的索引，用于后续生成目标序列
        emotion_cause_edge_indices = []

        # 添加情感原因对边
        for i, (src, trg) in enumerate(links):
            # 跳过无效的边
            if src < 0 or src >= dialog_length or trg < 0 or trg >= dialog_length:
                continue

            edge_index_list.append([src, trg])

            # 简化的边类型：情感-原因对边统一使用类型0
            edge_type_list.append(EDGE_TYPE_EMOTION_CAUSE)
            edge_norm_list.append(1.0)  # 情感-原因对边权重为1.0

            # 记录情感-原因对边的索引
            emotion_cause_edge_indices.append(len(edge_index_list) - 1)

        # 添加时序边 - 修复后的逻辑
        # 明确区分前向时序边(i->j, i<j)和后向时序边(i->j, i>j)
        # 这样模型可以学习到不同的时序依赖关系
        self._add_temporal_edges(dialog_length, utt_speakers, edge_index_list, edge_type_list, edge_norm_list)

        # 添加说话者关系边
        # 同说话者边：连接同一说话者的所有话语，捕获说话者一致性
        # 不同说话者边：连接相邻的不同说话者话语，捕获对话交互
        self._add_speaker_edges(dialog_length, utt_speakers, edge_index_list, edge_type_list, edge_norm_list)

        # 添加情感语义边
        # 相同情感边、情感转换边、情感对比边
        self._add_emotion_semantic_edges(dialog_length, utt_emotions, edge_index_list, edge_type_list, edge_norm_list)

        # 添加语义相似边
        # 基于话语文本内容的语义相似性
        self._add_semantic_similarity_edges(dialog_length, tokens, edge_index_list, edge_type_list, edge_norm_list)

        # 添加因果依赖边
        # 基于因果关键词检测的因果关系
        self._add_causal_dependency_edges(dialog_length, tokens, edge_index_list, edge_type_list, edge_norm_list)

        # 添加对话结构边
        # 问答对边、回应关系边、话题转换边
        self._add_dialog_structure_edges(dialog_length, tokens, utt_speakers, edge_index_list, edge_type_list, edge_norm_list)

        # 处理token序列
        dialog_tokens = [self.CLS]
        dialog_uttid = [0]

        for i, t in enumerate(tokens):
            t.append(self.SEP)
            dialog_tokens = dialog_tokens + t
            dialog_uttid = dialog_uttid + [i+1] * len(t)

        dialog_mask = [1] * len(dialog_tokens)
        utt_mask = [1] * (dialog_uttid[-1])

        dialog_tokens = self._pad(dialog_tokens, self.PAD, self.input_length)
        dialog_uttid = self._pad(dialog_uttid, 0, self.input_length)
        dialog_mask = self._pad(dialog_mask, 0, self.input_length)

        utt_mask = self._pad(utt_mask, 0, self.max_n_utt)
        utt_speakers = self._pad(utt_speakers, 0, self.max_n_utt)
        utt_emotions = self._pad(utt_emotions, 0, self.max_n_utt)

        # 转换为tensor（除了图结构相关的部分）
        dialog_tokens = torch.tensor(dialog_tokens)     # tokens of the whole dialog: [CLS] utt1 [SEP] utt2 [SEP] ... uttN [SEP]
        dialog_uttid = torch.tensor(dialog_uttid)       # utterance index of the token, -1 stands for padding or 'belongs to no utterance'
        dialog_mask = torch.tensor(dialog_mask)         # mask for tokens

        utt_mask = torch.tensor(utt_mask)               # mask for the utterances of shape (self.max_n_utt)
        utt_speakers = torch.tensor(utt_speakers)       # speaker index of the utterance, 0 means no speaker
        utt_emotions = torch.tensor(utt_emotions)       # emmotion of the utterance

        return dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, utt_emotions, edge_index_list, edge_type_list, edge_norm_list, emotion_cause_edge_indices

    def _add_temporal_edges(self, dialog_length, utt_speakers, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加时序边：明确区分前向和后向时序关系

        Args:
            dialog_length: 对话长度
            utt_speakers: 说话者列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        for i in range(dialog_length):
            # 添加前向时序边 (i -> i+1, i+2, ...)
            for j in range(i + 1, min(dialog_length, i + self.window_future + 1)):
                edge_index_list.append([i, j])
                edge_type_list.append(EDGE_TYPE_TEMPORAL_FORWARD)

                # 根据距离设置权重：距离越近权重越大
                distance = j - i
                weight = 1.0 / (1.0 + 0.1 * distance)  # 距离权重衰减
                edge_norm_list.append(weight)

            # 添加后向时序边 (i -> i-1, i-2, ...)
            for j in range(max(0, i - self.window_past), i):
                edge_index_list.append([i, j])
                edge_type_list.append(EDGE_TYPE_TEMPORAL_BACKWARD)

                # 根据距离设置权重
                distance = i - j
                weight = 1.0 / (1.0 + 0.1 * distance)  # 距离权重衰减
                edge_norm_list.append(weight)

    def _add_speaker_edges(self, dialog_length, utt_speakers, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加说话者关系边：连接同一说话者的话语和不同说话者的相邻话语

        Args:
            dialog_length: 对话长度
            utt_speakers: 说话者列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 同说话者边：连接同一说话者的所有话语
        for i in range(dialog_length):
            for j in range(i + 1, dialog_length):
                if utt_speakers[i] == utt_speakers[j] and utt_speakers[i] != 0:  # 排除padding
                    # 双向连接同一说话者的话语
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_SAME_SPEAKER)
                    edge_norm_list.append(0.7)  # 同说话者边权重

                    edge_index_list.append([j, i])
                    edge_type_list.append(EDGE_TYPE_SAME_SPEAKER)
                    edge_norm_list.append(0.7)

        # 不同说话者边：连接相邻的不同说话者话语（对话交互）
        for i in range(dialog_length - 1):
            j = i + 1
            if utt_speakers[i] != utt_speakers[j] and utt_speakers[i] != 0 and utt_speakers[j] != 0:
                # 双向连接相邻的不同说话者话语
                edge_index_list.append([i, j])
                edge_type_list.append(EDGE_TYPE_DIFF_SPEAKER)
                edge_norm_list.append(0.6)  # 不同说话者边权重

                edge_index_list.append([j, i])
                edge_type_list.append(EDGE_TYPE_DIFF_SPEAKER)
                edge_norm_list.append(0.6)

    def _add_emotion_semantic_edges(self, dialog_length, utt_emotions, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加情感语义边：捕获情感层面的语义关系

        Args:
            dialog_length: 对话长度
            utt_emotions: 情感标签列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 定义情感对比关系（相反情感）
        emotion_contrasts = {
            1: [3, 4, 6, 7],  # surprise vs sadness, neutral, disgust, anger
            2: [3, 4, 6, 7],  # joy vs sadness, neutral, disgust, anger
            3: [1, 2],        # sadness vs surprise, joy
            4: [1, 2, 6, 7],  # neutral vs surprise, joy, disgust, anger
            5: [1, 2],        # disgust vs surprise, joy
            6: [1, 2, 4],     # anger vs surprise, joy, neutral
            7: [2, 4]         # fear vs joy, neutral
        }

        for i in range(dialog_length):
            for j in range(i + 1, dialog_length):
                emotion_i = utt_emotions[i]
                emotion_j = utt_emotions[j]

                # 跳过无效情感（padding或_NONE）
                if emotion_i == 0 or emotion_j == 0:
                    continue

                # 1. 相同情感边
                if emotion_i == emotion_j:
                    # 双向连接具有相同情感的话语
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_SAME_EMOTION)
                    # 根据距离设置权重：距离越近权重越大
                    distance = j - i
                    weight = 0.8 / (1.0 + 0.05 * distance)
                    edge_norm_list.append(weight)

                    edge_index_list.append([j, i])
                    edge_type_list.append(EDGE_TYPE_SAME_EMOTION)
                    edge_norm_list.append(weight)

                # 2. 情感转换边（相邻话语的情感变化）
                elif j == i + 1:  # 只考虑相邻话语
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_EMOTION_TRANSITION)
                    edge_norm_list.append(0.7)  # 情感转换边权重

                # 3. 情感对比边（相反情感）
                elif emotion_j in emotion_contrasts.get(emotion_i, []):
                    # 双向连接具有对比情感的话语
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_EMOTION_CONTRAST)
                    # 对比边权重相对较低，但仍有意义
                    distance = j - i
                    weight = 0.5 / (1.0 + 0.1 * distance)
                    edge_norm_list.append(weight)

                    edge_index_list.append([j, i])
                    edge_type_list.append(EDGE_TYPE_EMOTION_CONTRAST)
                    edge_norm_list.append(weight)

    def _add_semantic_similarity_edges(self, dialog_length, tokens, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加语义相似边：基于文本内容的语义相似性

        Args:
            dialog_length: 对话长度
            tokens: 话语的token列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 计算话语间的语义相似度
        similarity_threshold = 0.3  # 相似度阈值

        for i in range(dialog_length):
            for j in range(i + 1, dialog_length):
                # 跳过距离过远的话语（减少计算量）
                if j - i > 10:  # 只考虑距离不超过10的话语对
                    continue

                # 计算两个话语的语义相似度
                similarity = self._calculate_text_similarity(tokens[i], tokens[j])

                # 如果相似度超过阈值，添加语义相似边
                if similarity > similarity_threshold:
                    # 双向连接语义相似的话语
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_SEMANTIC_SIMILAR)
                    # 权重基于相似度和距离
                    distance = j - i
                    weight = similarity * 0.6 / (1.0 + 0.05 * distance)
                    edge_norm_list.append(weight)

                    edge_index_list.append([j, i])
                    edge_type_list.append(EDGE_TYPE_SEMANTIC_SIMILAR)
                    edge_norm_list.append(weight)

    def _calculate_text_similarity(self, tokens1, tokens2):
        """
        计算两个话语的文本相似度（基于词汇重叠）

        Args:
            tokens1: 第一个话语的token列表
            tokens2: 第二个话语的token列表

        Returns:
            similarity: 相似度分数 (0-1)
        """
        # 转换为集合以计算交集和并集
        set1 = set(tokens1)
        set2 = set(tokens2)

        # 移除特殊token
        special_tokens = {self.CLS, self.SEP, self.PAD}
        set1 = set1 - special_tokens
        set2 = set2 - special_tokens

        # 如果任一集合为空，返回0
        if len(set1) == 0 or len(set2) == 0:
            return 0.0

        # 计算Jaccard相似度
        intersection = len(set1 & set2)
        union = len(set1 | set2)

        if union == 0:
            return 0.0

        jaccard_similarity = intersection / union

        # 考虑长度相似性（长度相近的话语更可能相似）
        len1, len2 = len(set1), len(set2)
        length_similarity = min(len1, len2) / max(len1, len2)

        # 综合相似度：Jaccard相似度 + 长度相似性
        combined_similarity = 0.7 * jaccard_similarity + 0.3 * length_similarity

        return combined_similarity

    def _add_causal_dependency_edges(self, dialog_length, tokens, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加因果依赖边：基于因果关键词检测的因果关系

        Args:
            dialog_length: 对话长度
            tokens: 话语的token列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 定义因果关键词及其权重
        causal_keywords = {
            # 直接因果关系词（高权重）
            'because': 1.0,
            'since': 0.9,
            'as': 0.8,
            'due': 0.9,  # "due to"
            'owing': 0.8,  # "owing to"
            'thanks': 0.7,  # "thanks to"

            # 结果关系词（中等权重）
            'so': 0.8,
            'therefore': 0.9,
            'thus': 0.8,
            'hence': 0.8,
            'consequently': 0.9,
            'accordingly': 0.7,
            'result': 0.8,  # "as a result"

            # 条件关系词（较低权重）
            'if': 0.6,
            'when': 0.5,
            'after': 0.6,
            'before': 0.5,

            # 情感因果词（ECPE特定）
            'upset': 0.7,
            'angry': 0.7,
            'happy': 0.7,
            'sad': 0.7,
            'worried': 0.7,
            'excited': 0.7,
            'disappointed': 0.7,
            'surprised': 0.7,
            'afraid': 0.7,
            'annoyed': 0.7,
            'frustrated': 0.7,
            'pleased': 0.7,
            'relieved': 0.7,
            'concerned': 0.7,
            'bothered': 0.7,
            'thrilled': 0.7
        }

        # 因果短语模式（需要特殊处理）
        causal_phrases = {
            'due to': 1.0,
            'owing to': 0.8,
            'thanks to': 0.7,
            'as a result': 0.9,
            'as a consequence': 0.9,
            'for this reason': 0.8,
            'that is why': 0.9,
            'this is because': 1.0,
            'the reason is': 0.9,
            'leads to': 0.8,
            'results in': 0.8,
            'causes': 0.9,
            'makes me': 0.7,  # "makes me feel"
            'made me': 0.7,   # "made me feel"
        }

        # 搜索窗口大小
        search_window = 3

        for i in range(dialog_length):
            # 获取当前话语的token（转换为小写字符串）
            current_tokens = self._tokens_to_text(tokens[i]).lower()

            # 检测当前话语中的因果关键词
            causal_score = self._calculate_causal_score(current_tokens, causal_keywords, causal_phrases)

            # 如果当前话语包含因果关键词，向前搜索原因
            if causal_score > 0:
                # 向前搜索窗口内的话语作为潜在原因
                start_idx = max(0, i - search_window)

                for j in range(start_idx, i):
                    # 计算因果依赖边的权重
                    distance = i - j
                    distance_decay = 1.0 / (1.0 + 0.2 * distance)  # 距离衰减

                    # 综合权重：因果关键词密度 × 距离衰减 × 基础权重
                    edge_weight = causal_score * distance_decay * 0.8

                    # 只添加权重超过阈值的边
                    if edge_weight > 0.1:
                        edge_index_list.append([j, i])  # j是原因，i是结果
                        edge_type_list.append(EDGE_TYPE_CAUSAL_DEPENDENCY)
                        edge_norm_list.append(edge_weight)

    def _tokens_to_text(self, token_list):
        """
        将token列表转换为文本字符串

        Args:
            token_list: token ID列表

        Returns:
            text: 文本字符串
        """
        # 这里简化处理，实际应该使用tokenizer.decode
        # 但为了避免复杂的tokenizer操作，我们使用简化的方法
        if hasattr(self, 'tokenizer') and hasattr(self.tokenizer, 'decode'):
            try:
                # 过滤特殊token
                filtered_tokens = [t for t in token_list if t not in [self.CLS, self.SEP, self.PAD]]
                return self.tokenizer.decode(filtered_tokens, skip_special_tokens=True)
            except:
                pass

        # 备用方法：直接将token ID转换为字符串（用于测试）
        return ' '.join([str(t) for t in token_list if t not in [self.CLS, self.SEP, self.PAD]])

    def _calculate_causal_score(self, text, causal_keywords, causal_phrases):
        """
        计算文本中的因果关键词密度分数

        Args:
            text: 输入文本
            causal_keywords: 因果关键词字典
            causal_phrases: 因果短语字典

        Returns:
            score: 因果关键词密度分数
        """
        total_score = 0.0
        word_count = len(text.split())

        if word_count == 0:
            return 0.0

        # 检查因果短语（优先级更高）
        for phrase, weight in causal_phrases.items():
            if phrase in text:
                total_score += weight

        # 检查单个因果关键词
        words = text.split()
        for word in words:
            # 移除标点符号
            clean_word = re.sub(r'[^\w]', '', word)
            if clean_word in causal_keywords:
                total_score += causal_keywords[clean_word]

        # 归一化：考虑文本长度，避免长文本获得过高分数
        normalized_score = total_score / max(1, word_count / 5)  # 每5个词为一个单位

        # 限制最大分数
        return min(normalized_score, 1.0)

    def _add_dialog_structure_edges(self, dialog_length, tokens, utt_speakers, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加对话结构边：问答对边、回应关系边、话题转换边

        Args:
            dialog_length: 对话长度
            tokens: 话语的token列表
            utt_speakers: 说话者列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 1. 添加问答对边
        self._add_question_answer_edges(dialog_length, tokens, utt_speakers, edge_index_list, edge_type_list, edge_norm_list)

        # 2. 添加回应关系边
        self._add_response_relation_edges(dialog_length, tokens, utt_speakers, edge_index_list, edge_type_list, edge_norm_list)

        # 3. 添加话题转换边
        self._add_topic_shift_edges(dialog_length, tokens, edge_index_list, edge_type_list, edge_norm_list)

    def _add_question_answer_edges(self, dialog_length, tokens, utt_speakers, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加问答对边：识别问题和对应的回答

        Args:
            dialog_length: 对话长度
            tokens: 话语的token列表
            utt_speakers: 说话者列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 问题标识词
        question_indicators = {
            'what', 'where', 'when', 'why', 'how', 'who', 'which', 'whose',
            'can', 'could', 'would', 'should', 'will', 'do', 'does', 'did',
            'is', 'are', 'was', 'were', 'have', 'has', 'had'
        }

        # 问号检测（简化版）
        question_punctuation = ['?', '？']

        for i in range(dialog_length):
            # 检测当前话语是否为问题
            current_text = self._tokens_to_text(tokens[i]).lower()
            is_question = False
            question_score = 0.0

            # 检查问号
            for punct in question_punctuation:
                if punct in current_text:
                    is_question = True
                    question_score += 0.8
                    break

            # 检查疑问词（句首）
            words = current_text.split()
            if words and words[0] in question_indicators:
                is_question = True
                question_score += 0.6

            # 检查助动词倒装（简化检测）
            if len(words) >= 2 and words[0] in {'can', 'could', 'would', 'should', 'will', 'do', 'does', 'did', 'is', 'are', 'was', 'were'}:
                is_question = True
                question_score += 0.5

            # 如果是问题，寻找后续的回答
            if is_question and question_score > 0:
                # 在接下来的3个话语中寻找回答（通常是不同说话者）
                for j in range(i + 1, min(dialog_length, i + 4)):
                    # 优先考虑不同说话者的回答
                    if utt_speakers[i] != utt_speakers[j]:
                        # 问题 -> 回答的边
                        edge_index_list.append([i, j])
                        edge_type_list.append(EDGE_TYPE_QUESTION_ANSWER)

                        # 权重基于问题置信度和距离
                        distance = j - i
                        weight = question_score * 0.8 / (1.0 + 0.3 * distance)
                        edge_norm_list.append(weight)
                        break  # 只连接第一个可能的回答

    def _add_response_relation_edges(self, dialog_length, tokens, utt_speakers, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加回应关系边：识别直接回应关系

        Args:
            dialog_length: 对话长度
            tokens: 话语的token列表
            utt_speakers: 说话者列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 回应标识词
        response_indicators = {
            # 同意/确认
            'yes': 0.9, 'yeah': 0.8, 'sure': 0.7, 'okay': 0.7, 'ok': 0.7,
            'right': 0.6, 'exactly': 0.8, 'absolutely': 0.8, 'definitely': 0.7,

            # 否定/反对
            'no': 0.9, 'nope': 0.8, 'never': 0.7, 'not': 0.6,

            # 回应性词汇
            'well': 0.5, 'actually': 0.6, 'but': 0.7, 'however': 0.7,
            'though': 0.6, 'still': 0.5, 'anyway': 0.6,

            # 情感回应
            'sorry': 0.7, 'thanks': 0.7, 'wow': 0.6, 'oh': 0.5,
            'really': 0.6, 'seriously': 0.6, 'amazing': 0.6, 'great': 0.6
        }

        for i in range(1, dialog_length):  # 从第二个话语开始
            current_text = self._tokens_to_text(tokens[i]).lower()
            words = current_text.split()

            # 计算回应分数
            response_score = 0.0
            for word in words[:3]:  # 只检查前3个词
                clean_word = re.sub(r'[^\w]', '', word)
                if clean_word in response_indicators:
                    response_score += response_indicators[clean_word]

            # 如果有回应特征，连接到前面的话语
            if response_score > 0.3:
                # 优先连接到前一个不同说话者的话语
                for j in range(i - 1, max(-1, i - 3), -1):
                    if utt_speakers[i] != utt_speakers[j]:
                        # 被回应的话语 -> 回应话语
                        edge_index_list.append([j, i])
                        edge_type_list.append(EDGE_TYPE_RESPONSE_RELATION)

                        # 权重基于回应分数和距离
                        distance = i - j
                        weight = min(response_score, 1.0) * 0.7 / (1.0 + 0.2 * distance)
                        edge_norm_list.append(weight)
                        break

    def _add_topic_shift_edges(self, dialog_length, tokens, edge_index_list, edge_type_list, edge_norm_list):
        """
        添加话题转换边：识别话题转换点

        Args:
            dialog_length: 对话长度
            tokens: 话语的token列表
            edge_index_list: 边索引列表
            edge_type_list: 边类型列表
            edge_norm_list: 边权重列表
        """
        # 话题转换标识词
        topic_shift_indicators = {
            # 直接转换
            'anyway': 0.8, 'anyways': 0.8, 'meanwhile': 0.7, 'besides': 0.7,
            'incidentally': 0.8, 'by the way': 0.9, 'speaking of': 0.9,

            # 时间转换
            'later': 0.6, 'then': 0.5, 'next': 0.6, 'after': 0.5,
            'before': 0.5, 'meanwhile': 0.7, 'afterwards': 0.6,

            # 对比转换
            'however': 0.7, 'but': 0.6, 'although': 0.6, 'though': 0.5,
            'on the other hand': 0.8, 'in contrast': 0.7,

            # 新话题引入
            'let me': 0.6, 'i want': 0.5, 'i need': 0.5, 'i think': 0.4,
            'you know': 0.5, 'listen': 0.7, 'look': 0.6, 'wait': 0.6,

            # 总结和转换
            'so': 0.5, 'well': 0.4, 'now': 0.5, 'okay': 0.4, 'alright': 0.5
        }

        # 话题转换短语
        topic_shift_phrases = {
            'by the way': 0.9,
            'speaking of': 0.9,
            'on the other hand': 0.8,
            'in contrast': 0.7,
            'let me tell you': 0.7,
            'you know what': 0.6,
            'i just remembered': 0.7,
            'that reminds me': 0.8
        }

        for i in range(1, dialog_length):  # 从第二个话语开始
            current_text = self._tokens_to_text(tokens[i]).lower()

            # 计算话题转换分数
            shift_score = self._calculate_topic_shift_score(current_text, topic_shift_indicators, topic_shift_phrases)

            # 如果检测到话题转换
            if shift_score > 0.3:
                # 连接到前面的话语，表示话题转换
                prev_idx = i - 1

                # 话题转换边：前一个话语 -> 当前话语（话题转换点）
                edge_index_list.append([prev_idx, i])
                edge_type_list.append(EDGE_TYPE_TOPIC_SHIFT)

                # 权重基于转换分数
                weight = min(shift_score, 1.0) * 0.6
                edge_norm_list.append(weight)

                # 如果转换分数很高，也可以连接到更早的话语
                if shift_score > 0.7 and i >= 2:
                    edge_index_list.append([i - 2, i])
                    edge_type_list.append(EDGE_TYPE_TOPIC_SHIFT)
                    edge_norm_list.append(weight * 0.7)  # 稍低的权重

    def _calculate_topic_shift_score(self, text, shift_indicators, shift_phrases):
        """
        计算话题转换分数

        Args:
            text: 输入文本
            shift_indicators: 话题转换标识词字典
            shift_phrases: 话题转换短语字典

        Returns:
            score: 话题转换分数
        """
        total_score = 0.0
        word_count = len(text.split())

        if word_count == 0:
            return 0.0

        # 检查话题转换短语（优先级更高）
        for phrase, weight in shift_phrases.items():
            if phrase in text:
                total_score += weight

        # 检查单个转换标识词
        words = text.split()
        for i, word in enumerate(words):
            clean_word = re.sub(r'[^\w]', '', word)
            if clean_word in shift_indicators:
                # 句首的转换词权重更高
                position_weight = 1.2 if i == 0 else 1.0
                total_score += shift_indicators[clean_word] * position_weight

        # 特殊模式检测
        # 1. 问句后的陈述（可能是话题转换）
        if '?' in text or '？' in text:
            total_score += 0.3

        # 2. 长停顿后的话语（用句号数量近似）
        period_count = text.count('.') + text.count('。')
        if period_count >= 2:
            total_score += 0.2

        # 归一化：考虑文本长度
        normalized_score = total_score / max(1, word_count / 8)  # 每8个词为一个单位

        # 限制最大分数
        return min(normalized_score, 1.0)

    def __getitem__(self, idx):
        return self.d[idx]


def collate_fn(batch):
    """
    自定义的批次处理函数，用于处理DataLoader的批次
    Args:
        batch: 包含多个样本数据的列表
    Returns:
        处理后的批次数据
    """
    # 对于10个返回项分别收集到列表中
    dialog_tokens_batch = []
    dialog_uttid_batch = []
    dialog_mask_batch = []
    utt_mask_batch = []
    utt_speakers_batch = []
    utt_emotions_batch = []
    edge_index_batch = []
    edge_type_batch = []
    edge_norm_batch = []
    emotion_cause_edge_indices_batch = []
    
    # 确保batch不为空
    if not batch:
        # 返回空的批次
        return (
            torch.zeros(0, 0), torch.zeros(0, 0), torch.zeros(0, 0),
            torch.zeros(0, 0), torch.zeros(0, 0), torch.zeros(0, 0),
            [], [], [], []
        )
        
    for sample in batch:
        if len(sample) >= 10:  # 确保样本包含所有必要元素
            dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, \
            utt_emotions, edge_index, edge_type, edge_norm, emotion_cause_edge_indices = sample
            
            # 确保tensor类型
            if isinstance(dialog_tokens, torch.Tensor):
                dialog_tokens_batch.append(dialog_tokens)
                dialog_uttid_batch.append(dialog_uttid)
                dialog_mask_batch.append(dialog_mask)
                utt_mask_batch.append(utt_mask)
                utt_speakers_batch.append(utt_speakers)
                utt_emotions_batch.append(utt_emotions)
                
                # 处理边数据 - 列表类型
                edge_index_batch.append(edge_index)
                edge_type_batch.append(edge_type)
                edge_norm_batch.append(edge_norm)
                emotion_cause_edge_indices_batch.append(emotion_cause_edge_indices)
    
    # 检查是否有有效数据
    if not dialog_tokens_batch:
        # 返回空的批次
        return (
            torch.zeros(0, 0), torch.zeros(0, 0), torch.zeros(0, 0),
            torch.zeros(0, 0), torch.zeros(0, 0), torch.zeros(0, 0),
            [], [], [], []
        )
    
    # 组装批次数据
    return (
        torch.stack(dialog_tokens_batch),
        torch.stack(dialog_uttid_batch),
        torch.stack(dialog_mask_batch),
        torch.stack(utt_mask_batch),
        torch.stack(utt_speakers_batch),
        torch.stack(utt_emotions_batch),
        edge_index_batch,
        edge_type_batch,
        edge_norm_batch,
        emotion_cause_edge_indices_batch
    )


if __name__ == '__main__':
    ds = DialogDataset("test")

    for x in ds:
        print(x)

