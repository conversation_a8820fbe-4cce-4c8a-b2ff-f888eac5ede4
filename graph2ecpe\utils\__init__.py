"""
Utils module for Graph2Seq ECPE project.

This module contains utility functions and data structures.
"""

from .graphify import batch_graphify, create_utterance_features, edge_perms
from .utils import utterances_to_dialogues, prepare_target_sequences, compute_loss, compute_ecpe_metrics, extract_pairs_from_sequence, target_seq_from_links
from .EmotionCauseLink import EmotionCauseLink
from .UtteranceItem import UtteranceItem
# 统一后处理接口
from .unified_post_processor import (
    UnifiedPostProcessor,
    ProcessingMode,
    create_unified_processor,
    process_predictions_unified
)
from .advanced_loss import (
    LabelSmoothingCrossEntropy,
    FocalLoss,
    ECPETaskSpecificLoss,
    AdaptiveLossScheduler,
    create_advanced_loss_function
)

__all__ = [
    # 核心工具函数
    'batch_graphify',
    'create_utterance_features',
    'edge_perms',
    'utterances_to_dialogues',
    'prepare_target_sequences',
    'target_seq_from_links',
    'compute_loss',
    'compute_ecpe_metrics',
    'extract_pairs_from_sequence',

    # 数据结构
    'EmotionCauseLink',
    'UtteranceItem',

    # 统一后处理接口
    'UnifiedPostProcessor',
    'ProcessingMode',
    'create_unified_processor',
    'process_predictions_unified',

    # 损失函数
    'LabelSmoothingCrossEntropy',
    'FocalLoss',
    'ECPETaskSpecificLoss',
    'AdaptiveLossScheduler',
    'create_advanced_loss_function'
]