# GraphMFT
The official implementation of the paper "[GraphMFT: A graph network based multimodal fusion technique for emotion recognition in conversation](https://doi.org/10.1016/j.neucom.2023.126427)", which has been accepted by Neurocomputing.  
Authors: <AUTHORS>
Affiliation: Huazhong University of Science and Technology (HUST)  

## bibtex
```bibtex
@article{li2023graphmft,
    title={GraphMFT: A graph network based multimodal fusion technique for emotion recognition in conversation},
    author={<PERSON> and <PERSON> and <PERSON> and <PERSON><PERSON><PERSON><PERSON>},
    year={2023},
    journal = {Neurocomputing},
    volume = {550},
    pages = {126427},
    doi={10.1016/j.neucom.2023.126427}
}
```

## Requirement
Checking and installing environmental requirements
```python
pip install -r requirements.txt
```

## Acknowledgement
We are grateful to [MMGCN](https://github.com/hujingwen6666/MMGCN) for sharing their codes and datasets.