#!/usr/bin/env python3
"""
统一配置文件
整合所有数据集配置、模型配置和训练配置
"""

import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field


@dataclass
class DatasetConfig:
    """数据集配置"""
    name: str = "meld"
    root_path: str = field(default_factory=lambda: os.path.dirname(os.path.abspath(__file__)))
    
    # 数据集路径
    dataset_paths: Dict[str, str] = field(default_factory=lambda: {
        "meld": "data/meld/",
        "dailydialog": "data/dailydialog/",
        "iemocap": "data/iemocap/"
    })
    
    # 情感类别配置
    emotion_categories: Dict[str, List[str]] = field(default_factory=lambda: {
        "meld": ["_NONE", "surprise", "joy", "sadness", "neutral", "disgust", "anger", "fear"],
        "dailydialog": ["_NONE", "anger", "disgust", "fear", "happiness", "sadness", "surprise", "neutral"],
        "iemocap": ["_NONE", "anger", "happiness", "sadness", "neutral", "excitement", "frustration"]
    })
    
    # 说话者配置
    speakers: Dict[str, List[str]] = field(default_factory=lambda: {
        "meld": ["_NONE", "Chandler", "Joey", "Ross", "Rachel", "Monica", "Phoebe"],
        "dailydialog": ["_NONE", "speaker1", "speaker2"],
        "iemocap": ["_NONE", "Ses01F", "Ses01M", "Ses02F", "Ses02M", "Ses03F", "Ses03M", 
                   "Ses04F", "Ses04M", "Ses05F", "Ses05M"]
    })
    
    def get_data_path(self) -> str:
        """获取当前数据集的路径"""
        relative_path = self.dataset_paths.get(self.name, self.dataset_paths["meld"])
        return os.path.join(self.root_path, relative_path)
    
    def get_emotion_categories(self) -> List[str]:
        """获取当前数据集的情感类别"""
        return self.emotion_categories.get(self.name, self.emotion_categories["meld"])
    
    def get_speakers(self) -> List[str]:
        """获取当前数据集的说话者"""
        return self.speakers.get(self.name, self.speakers["meld"])


@dataclass
class ModelConfig:
    """模型配置"""
    # 预训练语言模型
    plm_model: str = "roberta"
    plm_path: str = "/userdata2/fengweijie/roberta"
    
    # 模型架构
    hidden_size: int = 256
    gnn_layers: int = 2
    decoder_layers: int = 1
    num_attention_heads: int = 8
    dropout: float = 0.2
    
    # 图编码器配置
    graph_encoder_type: str = "gat"  # 'gnn' 或 'gat'
    gat_layers: int = 3
    gat_heads: int = 4
    gat_dropout: float = 0.1
    
    # 图构建配置
    window_past: int = 3
    window_future: int = 3
    max_utterance_length: int = 128
    num_edge_types: int = 13
    
    # 邻居采样配置
    use_neighbor_sampling: bool = True
    neighbor_sample_size: int = 10
    sampling_strategy: str = "uniform"  # 'uniform', 'degree_based', 'attention_based'
    
    # 聚合器配置
    use_gated_aggregator: bool = True
    aggregator_type: str = "gated"  # 'mean', 'max', 'attention', 'gated'
    gate_activation: str = "sigmoid"
    
    # 解码器配置
    use_beam_search: bool = False
    beam_size: int = 5
    max_decode_length: int = 50
    
    # 后处理配置
    post_processing_mode: str = "optimal"  # 'simple', 'optimal', 'enhanced', 'auto'

    # 其他必需的配置
    freeze_plm: bool = False
    max_target_length: int = 50
    vocab_size: int = 0  # 将在运行时设置


@dataclass
class TrainingConfig:
    """训练配置"""
    # 基础训练参数
    batch_size: int = 8
    learning_rate: float = 2e-5
    epochs: int = 20
    warmup_steps: int = 100
    weight_decay: float = 0.01
    gradient_clip_norm: float = 1.0
    
    # 学习率调度
    lr_scheduler: str = "linear"  # 'linear', 'cosine', 'polynomial'
    lr_decay_factor: float = 0.1
    lr_patience: int = 3
    
    # 早停配置
    early_stopping: bool = True
    patience: int = 5
    min_delta: float = 1e-4
    
    # 损失函数配置
    loss_type: str = "cross_entropy"  # 'cross_entropy', 'focal', 'label_smoothing'
    label_smoothing: float = 0.1
    focal_alpha: float = 1.0
    focal_gamma: float = 2.0
    
    # 正则化
    dropout_rate: float = 0.2
    l2_regularization: float = 0.01
    
    # 验证和保存
    eval_steps: int = 100
    save_steps: int = 500
    save_total_limit: int = 3
    
    # 设备配置
    device: str = "cuda"
    mixed_precision: bool = True
    dataloader_num_workers: int = 4


@dataclass
class ExperimentConfig:
    """实验配置"""
    # 实验标识
    experiment_name: str = "graph2seq_ecpe"
    run_name: Optional[str] = None
    seed: int = 42
    
    # 输出路径
    output_dir: str = "output"
    log_dir: str = "logs"
    checkpoint_dir: str = "checkpoints"
    
    # 日志配置
    log_level: str = "INFO"
    log_to_file: bool = True
    log_to_console: bool = True
    
    # 评估配置
    eval_on_test: bool = True
    eval_metrics: List[str] = field(default_factory=lambda: ["precision", "recall", "f1"])
    
    # 调试配置
    debug_mode: bool = False
    profile_memory: bool = False
    profile_time: bool = False


class UnifiedConfig:
    """统一配置类"""
    
    def __init__(self, 
                 dataset_name: str = "meld",
                 config_preset: str = "default"):
        """
        初始化统一配置
        
        Args:
            dataset_name: 数据集名称
            config_preset: 配置预设 ('default', 'lightweight', 'performance', 'debug')
        """
        self.dataset = DatasetConfig(name=dataset_name)
        self.model = ModelConfig()
        self.training = TrainingConfig()
        self.experiment = ExperimentConfig()
        
        # 应用配置预设
        self._apply_preset(config_preset)
        
        # 根据数据集调整配置
        self._adjust_for_dataset()
    
    def _apply_preset(self, preset: str):
        """应用配置预设"""
        if preset == "lightweight":
            # 轻量级配置：快速训练和测试
            self.model.hidden_size = 128
            self.model.gnn_layers = 1
            self.model.decoder_layers = 1
            self.model.gat_layers = 2
            self.training.batch_size = 16
            self.training.epochs = 10
            
        elif preset == "performance":
            # 高性能配置：追求最佳效果
            self.model.hidden_size = 512
            self.model.gnn_layers = 4
            self.model.decoder_layers = 3
            self.model.gat_layers = 4
            self.model.gat_heads = 8
            self.training.batch_size = 8
            self.training.epochs = 25
            self.training.learning_rate = 1e-5
            
        elif preset == "debug":
            # 调试配置：快速迭代
            self.model.hidden_size = 64
            self.model.gnn_layers = 1
            self.training.batch_size = 2
            self.training.epochs = 2
            self.training.eval_steps = 10
            self.experiment.debug_mode = True
            
        # default预设保持默认值
    
    def _adjust_for_dataset(self):
        """根据数据集调整配置"""
        if self.dataset.name == "dailydialog":
            # DailyDialog数据集调整
            self.model.window_past = 2
            self.model.window_future = 2
            self.training.batch_size = 12
            
        elif self.dataset.name == "iemocap":
            # IEMOCAP数据集调整
            self.model.max_utterance_length = 256
            self.training.batch_size = 6
            self.training.learning_rate = 1e-5
    
    def update_from_args(self, args):
        """从命令行参数更新配置"""
        # 数据集配置
        if hasattr(args, 'dataset') and args.dataset != self.dataset.name:
            self.dataset.name = args.dataset
            # 重新调整数据集特定配置
            self._adjust_for_dataset()

        # 模型配置
        if hasattr(args, 'plm_model'):
            self.model.plm_model = args.plm_model
        if hasattr(args, 'plm_path'):
            self.model.plm_path = args.plm_path
        if hasattr(args, 'hidden_size') and args.hidden_size != 256:  # 只在非默认值时覆盖
            self.model.hidden_size = args.hidden_size
        if hasattr(args, 'gnn_layers') and args.gnn_layers != 2:  # 只在非默认值时覆盖
            self.model.gnn_layers = args.gnn_layers
        if hasattr(args, 'decoder_layers') and args.decoder_layers != 1:  # 只在非默认值时覆盖
            self.model.decoder_layers = args.decoder_layers
        if hasattr(args, 'use_beam_search'):
            self.model.use_beam_search = args.use_beam_search
        if hasattr(args, 'beam_size'):
            self.model.beam_size = args.beam_size
        if hasattr(args, 'post_processing_mode'):
            self.model.post_processing_mode = args.post_processing_mode
        
        # 训练配置
        if hasattr(args, 'batch_size') and args.batch_size != 8:  # 只在非默认值时覆盖
            self.training.batch_size = args.batch_size
        if hasattr(args, 'learning_rate') and args.learning_rate != 2e-5:  # 只在非默认值时覆盖
            self.training.learning_rate = args.learning_rate
        if hasattr(args, 'epochs') and args.epochs != 20:  # 只在非默认值时覆盖
            self.training.epochs = args.epochs
        if hasattr(args, 'weight_decay'):
            self.training.weight_decay = args.weight_decay
        if hasattr(args, 'dropout'):
            self.training.dropout_rate = args.dropout
        
        # 实验配置
        if hasattr(args, 'output_dir'):
            self.experiment.output_dir = args.output_dir
        if hasattr(args, 'experiment_name'):
            self.experiment.experiment_name = args.experiment_name
        if hasattr(args, 'seed'):
            self.experiment.seed = args.seed
        if hasattr(args, 'debug'):
            self.experiment.debug_mode = args.debug
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'dataset': self.dataset.__dict__,
            'model': self.model.__dict__,
            'training': self.training.__dict__,
            'experiment': self.experiment.__dict__
        }
    
    def print_config(self):
        """打印配置信息"""
        print("=" * 60)
        print("🔧 统一配置信息")
        print("=" * 60)
        
        print(f"\n📊 数据集配置:")
        print(f"  数据集: {self.dataset.name}")
        print(f"  数据路径: {self.dataset.get_data_path()}")
        print(f"  情感类别: {len(self.dataset.get_emotion_categories())} 个")
        print(f"  说话者: {len(self.dataset.get_speakers())} 个")
        
        print(f"\n🤖 模型配置:")
        print(f"  PLM模型: {self.model.plm_model}")
        print(f"  隐藏层大小: {self.model.hidden_size}")
        print(f"  GNN层数: {self.model.gnn_layers}")
        print(f"  解码器层数: {self.model.decoder_layers}")
        print(f"  图编码器: {self.model.graph_encoder_type}")
        print(f"  后处理模式: {self.model.post_processing_mode}")
        
        print(f"\n🏋️ 训练配置:")
        print(f"  批次大小: {self.training.batch_size}")
        print(f"  学习率: {self.training.learning_rate}")
        print(f"  训练轮数: {self.training.epochs}")
        print(f"  权重衰减: {self.training.weight_decay}")
        print(f"  早停: {self.training.early_stopping}")
        
        print(f"\n🧪 实验配置:")
        print(f"  实验名称: {self.experiment.experiment_name}")
        print(f"  输出目录: {self.experiment.output_dir}")
        print(f"  随机种子: {self.experiment.seed}")
        print(f"  调试模式: {self.experiment.debug_mode}")
        
        print("=" * 60)


# 全局配置实例
_global_config = None

def get_config() -> UnifiedConfig:
    """获取全局配置实例"""
    global _global_config
    if _global_config is None:
        _global_config = UnifiedConfig()
    return _global_config

def set_config(config: UnifiedConfig):
    """设置全局配置实例"""
    global _global_config
    _global_config = config

def create_config(dataset_name: str = "meld", 
                 config_preset: str = "default") -> UnifiedConfig:
    """创建配置实例"""
    return UnifiedConfig(dataset_name=dataset_name, config_preset=config_preset)
