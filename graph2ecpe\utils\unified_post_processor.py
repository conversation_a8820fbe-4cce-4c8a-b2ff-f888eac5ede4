#!/usr/bin/env python3
"""
统一后处理接口
整合所有后处理方法为一个统一的接口

支持的处理模式：
- 'simple': 基础后处理 (最快)
- 'optimal': 最优后处理 (最佳性能，F1=0.846)
- 'enhanced': 增强后处理 (最高质量)
- 'auto': 自动选择最佳模式
"""

import torch
import re
from typing import List, Tuple, Dict, Set, Optional, Union
from enum import Enum

# 内置extract_pairs_from_sequence函数，避免循环导入
def extract_pairs_from_sequence(tokens):
    """
    从token序列中提取情感-原因对（使用话语ID格式）
    """
    pairs = []

    if not tokens or len(tokens) < 4:
        return pairs

    # 移除特殊tokens并分割
    if '<sos>' in tokens:
        start_idx = tokens.index('<sos>') + 1
    else:
        start_idx = 0

    if '<eos>' in tokens:
        end_idx = tokens.index('<eos>')
        tokens = tokens[start_idx:end_idx]
    else:
        tokens = tokens[start_idx:]

    # 按<sep>分割序列
    segments = []
    current_segment = []

    for token in tokens:
        if token == '<sep>':
            if len(current_segment) >= 3:  # 至少需要speaker, emotion, cause_speaker
                segments.append(current_segment)
            current_segment = []
        else:
            current_segment.append(token)

    # 添加最后一个段落（如果没有以<sep>结尾）
    if len(current_segment) >= 3:
        segments.append(current_segment)

    # 从每个段落提取情感-原因对
    for segment in segments:
        if len(segment) >= 3:
            speaker = segment[0]
            emotion = segment[1]
            cause_speaker = segment[2]
            pairs.append((speaker, emotion, cause_speaker))

    return pairs

class ProcessingMode(Enum):
    """后处理模式枚举"""
    SIMPLE = "simple"
    OPTIMAL = "optimal"
    ENHANCED = "enhanced"
    AUTO = "auto"

class ConfidenceEstimator:
    """
    置信度估计器 (简化版本)
    """

    def __init__(self, token2idx: Dict, idx2token: Dict):
        self.token2idx = token2idx
        self.idx2token = idx2token

    def estimate_confidence(self, sequence: torch.Tensor) -> torch.Tensor:
        """
        估计序列的置信度分数

        Args:
            sequence: 输入序列 [seq_len]

        Returns:
            置信度分数 [seq_len]
        """
        # 简化的置信度估计：基于token类型
        confidences = torch.ones_like(sequence, dtype=torch.float)

        for i, token_id in enumerate(sequence):
            token = self.idx2token.get(token_id.item(), '<unk>')

            # 特殊token的置信度较低
            if token in ['<pad>', '<unk>', '_NONE']:
                confidences[i] = 0.1
            # 话语ID的置信度中等
            elif token.startswith('utt_'):
                confidences[i] = 0.7
            # 情感词的置信度较高
            elif token in ['joy', 'anger', 'sadness', 'surprise', 'fear', 'disgust', 'neutral']:
                confidences[i] = 0.9
            # 其他token的置信度中等
            else:
                confidences[i] = 0.5

        return confidences

class UnifiedPostProcessor:
    """
    统一后处理器
    
    整合所有后处理方法，提供统一接口
    """
    
    def __init__(self, 
                 mode: Union[str, ProcessingMode] = ProcessingMode.OPTIMAL,
                 emotion_categories: List[str] = None,
                 token2idx: Dict = None,
                 idx2token: Dict = None,
                 confidence_threshold: float = 0.3):
        """
        初始化统一后处理器
        
        Args:
            mode: 处理模式 ('simple', 'optimal', 'enhanced', 'auto')
            emotion_categories: 有效情感类别列表
            token2idx: token到索引的映射 (enhanced模式需要)
            idx2token: 索引到token的映射 (enhanced模式需要)
            confidence_threshold: 置信度阈值 (enhanced模式使用)
        """
        # 处理模式
        if isinstance(mode, str):
            self.mode = ProcessingMode(mode)
        else:
            self.mode = mode
            
        # 情感类别
        if emotion_categories is None:
            emotion_categories = ["_NONE", "surprise", "joy", "sadness", "neutral", "disgust", "anger", "fear"]
        self.emotion_categories = emotion_categories
        self.valid_emotions = set(self.emotion_categories)
        
        # 词汇表 (enhanced模式需要)
        self.token2idx = token2idx
        self.idx2token = idx2token
        self.confidence_threshold = confidence_threshold
        
        # 初始化各种处理器
        self._init_processors()
        
        # 性能统计
        self.stats = {
            'total_processed': 0,
            'mode_usage': {mode.value: 0 for mode in ProcessingMode},
            'processing_times': []
        }
    
    def _init_processors(self):
        """初始化各种处理器"""
        # 最优后处理器 (内联实现)
        self.emotion_similarity = {
            'happy': 'joy', 'sad': 'sadness', 'angry': 'anger',
            'scared': 'fear', 'surprised': 'surprise', 'disgusted': 'disgust',
            'calm': 'neutral', 'none': '_NONE'
        }
        
        # 增强后处理器 (如果需要的话，延迟初始化)
        self._enhanced_processor = None
        self._confidence_estimator = None
    
    def process(self, 
                tokens: List[str], 
                confidences: Optional[torch.Tensor] = None,
                force_mode: Optional[Union[str, ProcessingMode]] = None) -> List[Tuple[str, str, str]]:
        """
        主处理函数
        
        Args:
            tokens: 输入token序列
            confidences: 置信度分数 (可选)
            force_mode: 强制使用特定模式 (可选)
            
        Returns:
            处理后的情感-原因对列表 [(emo_utt_id, emotion, cause_utt_id), ...]
        """
        import time
        start_time = time.time()
        
        # 确定处理模式
        processing_mode = force_mode or self.mode
        if isinstance(processing_mode, str):
            processing_mode = ProcessingMode(processing_mode)
        
        # 自动模式选择
        if processing_mode == ProcessingMode.AUTO:
            processing_mode = self._auto_select_mode(tokens, confidences)
        
        # 执行处理
        try:
            if processing_mode == ProcessingMode.SIMPLE:
                result = self._simple_process(tokens)
            elif processing_mode == ProcessingMode.OPTIMAL:
                result = self._optimal_process(tokens)
            elif processing_mode == ProcessingMode.ENHANCED:
                result = self._enhanced_process(tokens, confidences)
            else:
                raise ValueError(f"Unknown processing mode: {processing_mode}")
            
            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats(processing_mode, processing_time)
            
            return result
            
        except Exception as e:
            # 错误回退：使用简单模式
            print(f"Warning: {processing_mode.value} mode failed ({e}), falling back to simple mode")
            result = self._simple_process(tokens)
            self._update_stats(ProcessingMode.SIMPLE, time.time() - start_time)
            return result
    
    def _auto_select_mode(self, tokens: List[str], confidences: Optional[torch.Tensor]) -> ProcessingMode:
        """
        自动选择最佳处理模式
        
        Args:
            tokens: 输入token序列
            confidences: 置信度分数
            
        Returns:
            选择的处理模式
        """
        # 基于输入复杂度选择模式
        token_count = len(tokens)
        
        # 简单情况：使用最优模式 (性能最佳)
        if token_count <= 10:
            return ProcessingMode.OPTIMAL
        
        # 中等复杂度：根据是否有置信度信息选择
        elif token_count <= 30:
            if confidences is not None and self.token2idx is not None:
                return ProcessingMode.ENHANCED  # 有置信度信息，使用增强模式
            else:
                return ProcessingMode.OPTIMAL   # 无置信度信息，使用最优模式
        
        # 复杂情况：使用简单模式 (最快)
        else:
            return ProcessingMode.SIMPLE
    
    def _simple_process(self, tokens: List[str]) -> List[Tuple[str, str, str]]:
        """简单后处理"""
        return extract_pairs_from_sequence(tokens)
    
    def _optimal_process(self, tokens: List[str]) -> List[Tuple[str, str, str]]:
        """最优后处理 (Aggressive策略)"""
        # 1. 基础提取
        pairs = extract_pairs_from_sequence(tokens)
        
        # 2. 智能错误修复
        fixed_pairs = self._fix_common_errors(pairs)
        
        # 3. 格式验证和清理
        validated_pairs = self._validate_and_clean(fixed_pairs)
        
        # 4. 去重（保持顺序）
        final_pairs = self._remove_duplicates_ordered(validated_pairs)
        
        return final_pairs
    
    def _enhanced_process(self, tokens: List[str], confidences: Optional[torch.Tensor]) -> List[Tuple[str, str, str]]:
        """增强后处理"""
        if self.token2idx is None or self.idx2token is None:
            # 如果没有词汇表，回退到最优处理
            return self._optimal_process(tokens)

        # 延迟初始化置信度估计器
        if self._confidence_estimator is None:
            self._confidence_estimator = ConfidenceEstimator(self.token2idx, self.idx2token)

        try:
            # 转换tokens为tensor
            unk_id = self.token2idx.get('_NONE', 0)
            token_ids = [self.token2idx.get(token, unk_id) for token in tokens]
            tensor = torch.tensor(token_ids)

            # 估计置信度 (如果没有提供)
            if confidences is None:
                confidences = self._confidence_estimator.estimate_confidence(tensor)

            # 应用增强处理：先用最优处理，然后基于置信度过滤
            optimal_pairs = self._optimal_process(tokens)

            # 基于置信度过滤结果
            filtered_pairs = self._confidence_filter(optimal_pairs, confidences, tokens)

            return filtered_pairs

        except Exception as e:
            # 增强处理失败，回退到最优处理
            print(f"Enhanced processing failed: {e}, falling back to optimal")
            return self._optimal_process(tokens)
    
    def _fix_common_errors(self, pairs: List[Tuple]) -> List[Tuple[str, str, str]]:
        """修复常见错误 (来自最优后处理器)"""
        fixed_pairs = []
        
        for pair in pairs:
            if len(pair) < 3:
                continue
                
            emo_utt, emotion, cause_utt = pair[0], pair[1], pair[2]
            
            # 修复1: 顺序错误
            if not emo_utt.startswith('utt_') and emotion.startswith('utt_'):
                emo_utt, emotion = emotion, emo_utt
            
            # 修复2: 情感类别错误
            emotion = self._fix_emotion(emotion)
            
            # 修复3: 话语ID格式错误
            emo_utt = self._fix_utterance_id(emo_utt)
            cause_utt = self._fix_utterance_id(cause_utt)
            
            # 修复4: 自指向问题
            if emo_utt == cause_utt:
                cause_utt = self._find_alternative_cause(emo_utt)
            
            # 如果修复成功，添加到结果
            if (emo_utt and emo_utt.startswith('utt_') and 
                emotion in self.valid_emotions and 
                cause_utt and cause_utt.startswith('utt_')):
                fixed_pairs.append((emo_utt, emotion, cause_utt))
        
        return fixed_pairs
    
    def _fix_emotion(self, emotion: str) -> str:
        """修复情感类别"""
        if emotion in self.valid_emotions:
            return emotion
        
        emotion_lower = emotion.lower()
        for valid_emotion in self.valid_emotions:
            if valid_emotion.lower() == emotion_lower:
                return valid_emotion
        
        if emotion_lower in self.emotion_similarity:
            return self.emotion_similarity[emotion_lower]
        
        for valid_emotion in self.valid_emotions:
            if (valid_emotion.lower() in emotion_lower or 
                emotion_lower in valid_emotion.lower()):
                return valid_emotion
        
        return 'neutral' if 'neutral' in self.valid_emotions else self.valid_emotions[0]
    
    def _fix_utterance_id(self, utt_id: str) -> str:
        """修复话语ID格式"""
        if not utt_id:
            return ""
        
        if re.match(r'^utt_\d{3}$', utt_id):
            return utt_id
        
        numbers = re.findall(r'\d+', utt_id)
        if numbers:
            num = int(numbers[0])
            return f"utt_{num:03d}"
        
        if 'utt' in utt_id.lower():
            match = re.search(r'utt[_\s]*(\d+)', utt_id.lower())
            if match:
                num = int(match.group(1))
                return f"utt_{num:03d}"
        
        return ""
    
    def _find_alternative_cause(self, emo_utt: str) -> str:
        """为自指向的情感话语找到替代原因"""
        match = re.search(r'utt_(\d+)', emo_utt)
        if not match:
            return "utt_000"
        
        current_num = int(match.group(1))
        if current_num > 0:
            return f"utt_{current_num-1:03d}"
        else:
            return f"utt_{current_num+1:03d}"
    
    def _validate_and_clean(self, pairs: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """验证和清理对"""
        validated_pairs = []
        
        for emo_utt, emotion, cause_utt in pairs:
            if (emo_utt.startswith('utt_') and 
                emotion in self.valid_emotions and 
                cause_utt.startswith('utt_') and
                emo_utt != cause_utt):
                validated_pairs.append((emo_utt, emotion, cause_utt))
        
        return validated_pairs
    
    def _remove_duplicates_ordered(self, pairs: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """去重但保持顺序"""
        seen = set()
        unique_pairs = []

        for pair in pairs:
            if pair not in seen:
                unique_pairs.append(pair)
                seen.add(pair)

        return unique_pairs

    def _confidence_filter(self, pairs: List[Tuple[str, str, str]],
                          confidences: torch.Tensor,
                          tokens: List[str]) -> List[Tuple[str, str, str]]:
        """
        基于置信度过滤情感-原因对

        Args:
            pairs: 候选对列表
            confidences: 置信度分数
            tokens: 原始token序列

        Returns:
            过滤后的对列表
        """
        if len(pairs) == 0:
            return pairs

        # 计算每个对的平均置信度
        filtered_pairs = []

        for pair in pairs:
            emo_utt, emotion, cause_utt = pair

            # 在tokens中找到这个对的位置，计算置信度
            pair_confidence = self._calculate_pair_confidence(pair, tokens, confidences)

            # 只保留置信度高于阈值的对
            if pair_confidence >= self.confidence_threshold:
                filtered_pairs.append(pair)

        # 如果过滤后没有结果，返回置信度最高的一个
        if len(filtered_pairs) == 0 and len(pairs) > 0:
            best_pair = max(pairs, key=lambda p: self._calculate_pair_confidence(p, tokens, confidences))
            filtered_pairs.append(best_pair)

        return filtered_pairs

    def _calculate_pair_confidence(self, pair: Tuple[str, str, str],
                                  tokens: List[str],
                                  confidences: torch.Tensor) -> float:
        """
        计算单个情感-原因对的置信度

        Args:
            pair: 情感-原因对
            tokens: token序列
            confidences: 置信度分数

        Returns:
            对的置信度分数
        """
        emo_utt, emotion, cause_utt = pair

        # 在tokens中找到这些token的位置
        token_confidences = []

        for i, token in enumerate(tokens):
            if token in [emo_utt, emotion, cause_utt]:
                if i < len(confidences):
                    token_confidences.append(confidences[i].item())

        # 返回平均置信度
        if token_confidences:
            return sum(token_confidences) / len(token_confidences)
        else:
            return 0.5  # 默认置信度
    
    def _update_stats(self, mode: ProcessingMode, processing_time: float):
        """更新统计信息"""
        self.stats['total_processed'] += 1
        self.stats['mode_usage'][mode.value] += 1
        self.stats['processing_times'].append(processing_time)
    
    def get_stats(self) -> Dict:
        """获取处理统计信息"""
        if self.stats['processing_times']:
            avg_time = sum(self.stats['processing_times']) / len(self.stats['processing_times'])
        else:
            avg_time = 0.0
        
        return {
            **self.stats,
            'average_processing_time_ms': avg_time * 1000,
            'recommended_mode': self._get_recommended_mode()
        }
    
    def _get_recommended_mode(self) -> str:
        """基于使用统计推荐最佳模式"""
        if self.stats['total_processed'] < 10:
            return ProcessingMode.OPTIMAL.value  # 默认推荐最优模式
        
        # 基于使用频率和性能推荐
        usage = self.stats['mode_usage']
        if usage[ProcessingMode.OPTIMAL.value] > usage[ProcessingMode.ENHANCED.value]:
            return ProcessingMode.OPTIMAL.value
        else:
            return ProcessingMode.ENHANCED.value

# 便捷函数
def create_unified_processor(mode: str = "optimal", **kwargs) -> UnifiedPostProcessor:
    """创建统一后处理器实例"""
    return UnifiedPostProcessor(mode=mode, **kwargs)

def process_predictions_unified(token_sequences: List[List[str]], 
                              mode: str = "optimal",
                              **kwargs) -> List[List[Tuple[str, str, str]]]:
    """
    批量处理预测序列
    
    Args:
        token_sequences: token序列列表
        mode: 处理模式
        **kwargs: 其他参数
        
    Returns:
        处理后的情感-原因对列表
    """
    processor = create_unified_processor(mode=mode, **kwargs)
    return [processor.process(tokens) for tokens in token_sequences]
