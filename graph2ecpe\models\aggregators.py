"""
聚合器模块

实现多种图神经网络聚合策略，用于融合节点自身特征和邻居特征。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class MeanAggregator(nn.Module):
    """
    均值聚合器
    
    简单地将自身特征和邻居特征的均值进行线性变换后相加。
    这是最基础的聚合策略，计算高效且稳定。
    """
    
    def __init__(self, input_dim, output_dim, dropout=0.0, bias=True, concat=False):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.concat = concat
        self.dropout = dropout
        
        # 如果使用拼接，输出维度翻倍
        if concat:
            self.output_dim = 2 * output_dim
        
        # 线性变换层
        self.self_transform = nn.Linear(input_dim, output_dim, bias=bias)
        self.neigh_transform = nn.Linear(input_dim, output_dim, bias=bias)
        
        # Dropout层
        self.dropout_layer = nn.Dropout(dropout)
        
    def forward(self, self_features, neighbor_features):
        """
        前向传播
        
        Args:
            self_features: 自身特征 [num_nodes, input_dim]
            neighbor_features: 邻居特征 [num_nodes, input_dim]
            
        Returns:
            aggregated_features: 聚合后的特征 [num_nodes, output_dim]
        """
        # 应用dropout
        if self.training:
            self_features = self.dropout_layer(self_features)
            neighbor_features = self.dropout_layer(neighbor_features)
        
        # 线性变换
        self_transformed = self.self_transform(self_features)
        neigh_transformed = self.neigh_transform(neighbor_features)
        
        # 聚合
        if self.concat:
            output = torch.cat([self_transformed, neigh_transformed], dim=-1)
        else:
            output = self_transformed + neigh_transformed
        
        return output


class MaxPoolingAggregator(nn.Module):
    """
    最大池化聚合器
    
    通过MLP变换邻居特征后进行最大池化，能够捕获最显著的邻居特征。
    """
    
    def __init__(self, input_dim, output_dim, hidden_dim=None, dropout=0.0, bias=True, concat=False):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.concat = concat
        
        if hidden_dim is None:
            hidden_dim = output_dim
        
        # 如果使用拼接，输出维度翻倍
        if concat:
            self.output_dim = 2 * output_dim
        
        # MLP层用于邻居特征变换
        self.neigh_mlp = nn.Sequential(
            nn.Linear(input_dim, hidden_dim, bias=bias),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim, bias=bias)
        )
        
        # 自身特征变换
        self.self_transform = nn.Linear(input_dim, output_dim, bias=bias)
        
        self.dropout_layer = nn.Dropout(dropout)
        
    def forward(self, self_features, neighbor_features_list):
        """
        前向传播
        
        Args:
            self_features: 自身特征 [num_nodes, input_dim]
            neighbor_features_list: 邻居特征列表，每个元素为 [num_neighbors, input_dim]
            
        Returns:
            aggregated_features: 聚合后的特征 [num_nodes, output_dim]
        """
        batch_size = self_features.size(0)
        
        # 处理邻居特征
        pooled_neighbors = []
        for i in range(batch_size):
            if len(neighbor_features_list[i]) > 0:
                # 通过MLP变换邻居特征
                neigh_transformed = self.neigh_mlp(neighbor_features_list[i])
                # 最大池化
                pooled = torch.max(neigh_transformed, dim=0)[0]
            else:
                # 如果没有邻居，使用零向量
                pooled = torch.zeros(self.output_dim, device=self_features.device)
            pooled_neighbors.append(pooled)
        
        pooled_neighbors = torch.stack(pooled_neighbors)
        
        # 应用dropout
        if self.training:
            self_features = self.dropout_layer(self_features)
            pooled_neighbors = self.dropout_layer(pooled_neighbors)
        
        # 自身特征变换
        self_transformed = self.self_transform(self_features)
        
        # 聚合
        if self.concat:
            output = torch.cat([self_transformed, pooled_neighbors], dim=-1)
        else:
            output = self_transformed + pooled_neighbors
        
        return output


class GatedMeanAggregator(nn.Module):
    """
    门控均值聚合器
    
    使用门控机制自适应地控制自身特征和邻居特征的融合比例。
    这是Graph2Seq论文中的核心创新，通常性能最佳。
    """
    
    def __init__(self, input_dim, output_dim, dropout=0.0, bias=True, concat=False):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.concat = concat
        
        # 如果使用拼接，输出维度翻倍
        if concat:
            self.output_dim = 2 * output_dim
        
        # 特征变换层
        self.self_transform = nn.Linear(input_dim, output_dim, bias=bias)
        self.neigh_transform = nn.Linear(input_dim, output_dim, bias=bias)
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(2 * output_dim, output_dim),
            nn.ReLU(),
            nn.Linear(output_dim, output_dim),
            nn.Sigmoid()
        )
        
        # Dropout和层归一化
        self.dropout_layer = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(self.output_dim)
        
    def forward(self, self_features, neighbor_features):
        """
        前向传播
        
        Args:
            self_features: 自身特征 [num_nodes, input_dim]
            neighbor_features: 邻居特征 [num_nodes, input_dim]
            
        Returns:
            aggregated_features: 聚合后的特征 [num_nodes, output_dim]
        """
        # 应用dropout
        if self.training:
            self_features = self.dropout_layer(self_features)
            neighbor_features = self.dropout_layer(neighbor_features)
        
        # 特征变换
        self_transformed = self.self_transform(self_features)
        neigh_transformed = self.neigh_transform(neighbor_features)
        
        # 门控机制
        gate_input = torch.cat([self_transformed, neigh_transformed], dim=-1)
        gate = self.gate_network(gate_input)
        
        # 门控融合
        if self.concat:
            output = torch.cat([self_transformed, neigh_transformed], dim=-1)
        else:
            output = gate * self_transformed + (1 - gate) * neigh_transformed
        
        # 层归一化
        output = self.layer_norm(output)
        
        return output


class ECPEAwareAggregator(GatedMeanAggregator):
    """
    ECPE任务感知的聚合器
    
    专门针对情感原因对提取任务设计，对情感-原因相关的特征给予更多关注。
    """
    
    def __init__(self, input_dim, output_dim, dropout=0.0, bias=True, concat=False, 
                 emotion_weight=2.0, cause_weight=1.5):
        super().__init__(input_dim, output_dim, dropout, bias, concat)
        
        self.emotion_weight = emotion_weight
        self.cause_weight = cause_weight
        
        # 情感-原因感知的注意力网络
        self.ec_attention = nn.Sequential(
            nn.Linear(output_dim, output_dim // 2),
            nn.ReLU(),
            nn.Linear(output_dim // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, self_features, neighbor_features, emotion_mask=None, cause_mask=None):
        """
        ECPE感知的前向传播
        
        Args:
            self_features: 自身特征 [num_nodes, input_dim]
            neighbor_features: 邻居特征 [num_nodes, input_dim]
            emotion_mask: 情感节点掩码 [num_nodes]
            cause_mask: 原因节点掩码 [num_nodes]
            
        Returns:
            aggregated_features: 聚合后的特征 [num_nodes, output_dim]
        """
        # 基础门控聚合
        output = super().forward(self_features, neighbor_features)
        
        # ECPE感知的增强
        if emotion_mask is not None or cause_mask is not None:
            # 计算注意力权重
            attention_weights = self.ec_attention(output)
            
            # 根据情感/原因掩码调整权重
            if emotion_mask is not None:
                emotion_boost = emotion_mask.float().unsqueeze(-1) * self.emotion_weight
                attention_weights = attention_weights * (1 + emotion_boost)
            
            if cause_mask is not None:
                cause_boost = cause_mask.float().unsqueeze(-1) * self.cause_weight
                attention_weights = attention_weights * (1 + cause_boost)
            
            # 应用注意力权重
            output = output * attention_weights
        
        return output


class AttentionAggregator(nn.Module):
    """
    注意力聚合器
    
    使用注意力机制对邻居特征进行加权聚合。
    """
    
    def __init__(self, input_dim, output_dim, num_heads=1, dropout=0.0, bias=True):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_heads = num_heads
        
        # 多头注意力
        self.attention = nn.MultiheadAttention(
            embed_dim=input_dim,
            num_heads=num_heads,
            dropout=dropout,
            bias=bias,
            batch_first=True
        )
        
        # 输出投影
        self.output_projection = nn.Linear(input_dim, output_dim, bias=bias)
        self.layer_norm = nn.LayerNorm(output_dim)
        
    def forward(self, self_features, neighbor_features):
        """
        前向传播
        
        Args:
            self_features: 自身特征 [num_nodes, input_dim]
            neighbor_features: 邻居特征 [num_nodes, input_dim]
            
        Returns:
            aggregated_features: 聚合后的特征 [num_nodes, output_dim]
        """
        # 准备注意力输入
        query = self_features.unsqueeze(1)  # [num_nodes, 1, input_dim]
        key = neighbor_features.unsqueeze(1)  # [num_nodes, 1, input_dim]
        value = neighbor_features.unsqueeze(1)  # [num_nodes, 1, input_dim]
        
        # 应用注意力
        attended_features, _ = self.attention(query, key, value)
        attended_features = attended_features.squeeze(1)  # [num_nodes, input_dim]
        
        # 输出投影和层归一化
        output = self.output_projection(attended_features)
        output = self.layer_norm(output)
        
        return output


def create_aggregator(aggregator_type='mean', **kwargs):
    """
    工厂函数：创建聚合器
    
    Args:
        aggregator_type: 聚合器类型 ('mean', 'max_pooling', 'gated_mean', 'ecpe_aware', 'attention')
        **kwargs: 聚合器参数
        
    Returns:
        Aggregator: 聚合器实例
    """
    if aggregator_type == 'mean':
        return MeanAggregator(**kwargs)
    elif aggregator_type == 'max_pooling':
        return MaxPoolingAggregator(**kwargs)
    elif aggregator_type == 'gated_mean':
        return GatedMeanAggregator(**kwargs)
    elif aggregator_type == 'ecpe_aware':
        return ECPEAwareAggregator(**kwargs)
    elif aggregator_type == 'attention':
        return AttentionAggregator(**kwargs)
    else:
        raise ValueError(f"Unknown aggregator type: {aggregator_type}")
